// Renderer process
const { ipc<PERSON>enderer } = require('electron');

// DOM Elements
const tabButtons = document.querySelectorAll('.tab-button');
const tabPanes = document.querySelectorAll('.tab-pane');
const statusText = document.getElementById('status-text');

// Setup tab elements
const urlInput = document.getElementById('url-input');
const pagesInput = document.getElementById('pages-input');
const playwrightCheckbox = document.getElementById('playwright-checkbox');
const seleniumCheckbox = document.getElementById('selenium-checkbox');
const driverInput = document.getElementById('driver-input');
const browseButton = document.getElementById('browse-button');
const startButton = document.getElementById('start-button');
const chatButton = document.getElementById('chat-button');

// Setup tab import elements
const quickImportButton = document.getElementById('quick-import-button');
const advancedImportButton = document.getElementById('advanced-import-button');
const analyzeAfterImportCheckbox = document.getElementById('analyze-after-import-checkbox');
const storeOriginalPathsCheckbox = document.getElementById('store-original-paths-checkbox');

// Import tab elements
const selectDocumentsButton = document.getElementById('select-documents-button');
const dropZone = document.getElementById('drop-zone');
const fileList = document.getElementById('file-list');
const importDocumentsButton = document.getElementById('import-documents-button');
const clearFilesButton = document.getElementById('clear-files-button');
const importStatus = document.getElementById('import-status');
const importedDocuments = document.getElementById('imported-documents');
const documentGrid = document.getElementById('document-grid');

// Crawl tab elements
const crawlStatus = document.getElementById('crawl-status');
const crawlProgress = document.getElementById('crawl-progress');
const logContainer = document.getElementById('log-container');

// Analysis tab elements
const analysisStatus = document.getElementById('analysis-status');
const modelSelect = document.getElementById('model-select');
const ragAnalysisButton = document.getElementById('rag-analysis-button');
const ragOnlyAnalysisButton = document.getElementById('rag-only-analysis-button');
const resultsContainer = document.getElementById('results-container');
// Note: directAnalysisButton has been removed

// RAG tab elements
const ragStatus = document.getElementById('rag-status');
const queryInput = document.getElementById('query-input');
const queryButton = document.getElementById('query-button');
const answerContainer = document.getElementById('answer-container');
const deepResearchToggle = document.getElementById('deep-research-toggle');
const deepResearchOptions = document.getElementById('deep-research-options');
const searchResultsCount = document.getElementById('search-results-count');
const saveToDatabaseCheckbox = document.getElementById('save-to-database-checkbox');
const attachFileButton = document.getElementById('attach-file-button');
const attachedFilePreview = document.getElementById('attached-file-preview');
const attachedFilename = document.getElementById('attached-filename');
const attachedFilesize = document.getElementById('attached-filesize');
const removeFileButton = document.getElementById('remove-file-button');

// State variables
let crawlComplete = false;
let ragIndexBuilt = false;
let waitingForModelSelection = false;
let pythonProcessRunning = false;
let selectedFiles = [];
let attachedFile = null; // For chat file attachment
let currentSessionId = null; // Current active session ID
let sessions = []; // List of available sessions
let systemInitialized = false; // Track if the system is fully initialized
let loadingProgress = 0; // Track loading progress

// User settings
const userSettings = {
  defaultAnalysisMethod: 'rag-only', // 'rag' or 'rag-only'
  defaultModel: 'mistralai/mistral-7b-instruct',
  autoSwitchToRag: true,
  openrouterApiKey: '', // Store API key
  deepResearchEnabled: false,
  deepResearchResultsCount: 3,
  deepResearchMaxSubQueries: 5, // Maximum number of sub-queries for multi-query research
  deepResearchUrlTimeout: 10, // Timeout per URL in seconds
  deepResearchSaveToDatabase: true,
  intelligentModeEnabled: false, // Intelligent Mode setting - will be overridden by localStorage if available
  autoSaveOperationSummaries: false, // Whether to automatically save operation summaries to the RAG system
  // Pinecone settings
  pineconeApiKey: '',
  pineconeEnvironment: '',
  pineconeIndexName: '',
  usePinecone: false
};

// Loading screen elements
const loadingScreen = document.getElementById('loading-screen');
const loadingStatus = document.getElementById('loading-status');
const loadingProgressBar = document.getElementById('loading-progress-bar');

// Loading screen functions
function updateLoadingStatus(message, progress = null) {
  if (loadingStatus) {
    loadingStatus.textContent = message;
  }

  if (progress !== null && loadingProgressBar) {
    loadingProgress = progress;
    loadingProgressBar.style.width = `${progress}%`;
  }
}

function hideLoadingScreen() {
  if (loadingScreen) {
    // Add the hidden class to trigger the fade-out animation
    loadingScreen.classList.add('hidden');

    // Set systemInitialized to true
    systemInitialized = true;

    // Update the status text to Ready
    updateStatus('Ready');

    // Also update the RAG status if we're on the RAG tab
    if (ragStatus) {
      ragStatus.textContent = 'Ready. Ask a question about the content.';
    }
  }
}

// Loading sequence
function startLoadingSequence() {
  // Initial loading status
  updateLoadingStatus('Initializing system...', 10);

  // Simulate loading progress
  let progress = 10;
  const progressInterval = setInterval(() => {
    progress += 2;
    if (progress > 90) {
      clearInterval(progressInterval);
    }
    updateLoadingStatus(null, progress);
  }, 200);

  // Listen for key initialization events
  const initEvents = {
    databaseReady: false,
    conversationsReady: false,
    pythonReady: false,
    uiReady: true // UI is ready immediately
  };

  // Listen for database status
  ipcRenderer.once('database-status', () => {
    initEvents.databaseReady = true;
    updateLoadingStatus('Database initialized...', 50);
    checkInitComplete();
  });

  // Listen for conversation manager ready
  ipcRenderer.once('get-sessions', () => {
    initEvents.conversationsReady = true;
    updateLoadingStatus('Sessions loaded...', 70);
    checkInitComplete();
  });

  // Listen for Python process ready
  ipcRenderer.once('python-output', () => {
    initEvents.pythonReady = true;
    pythonProcessRunning = true;
    updateLoadingStatus('Backend services ready...', 85);
    checkInitComplete();
  });

  // Function to check if initialization is complete
  function checkInitComplete() {
    const allReady = Object.values(initEvents).every(status => status);

    if (allReady) {
      updateLoadingStatus('Ready!', 100);

      // Update the main status indicators
      updateStatus('Ready');
      if (ragStatus) {
        ragStatus.textContent = 'Ready. Ask a question about the content.';
      }

      // Hide loading screen after a short delay
      setTimeout(() => {
        hideLoadingScreen();
      }, 500);
    }
  }

  // Fallback timer to hide loading screen after 30 seconds
  setTimeout(() => {
    if (!systemInitialized) {
      console.log('Loading timeout reached, hiding loading screen');
      updateLoadingStatus('Ready!', 100);

      // Update the main status indicators
      updateStatus('Ready');
      if (ragStatus) {
        ragStatus.textContent = 'Ready. Ask a question about the content.';
      }

      // Hide the loading screen
      hideLoadingScreen();
    }
  }, 30000);

  // Check if we're already initialized (for quick loads)
  setTimeout(checkInitComplete, 1000);
}

// Tab switching and initialization
document.addEventListener('DOMContentLoaded', () => {
  // Start the loading sequence
  startLoadingSequence();

  // Get all navigation buttons
  const navButtons = document.querySelectorAll('.nav-button');

  navButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.getAttribute('data-tab');

      // Update active nav button
      navButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // Update active tab pane
      document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
      document.getElementById(`${tabName}-tab`).classList.add('active');

      // Update status text
      updateStatus(`Viewing ${tabName}`);

      // Special handling for RAG tab
      if (tabName === 'rag' && ragIndexBuilt) {
        queryInput.focus();
      }
    });
  });

  // Ensure the chat tab (rag-tab) is active on startup
  // This is now handled in the HTML by setting the active class
  // But we'll make sure the nav button is also active
  navButtons.forEach(btn => {
    if (btn.getAttribute('data-tab') === 'rag') {
      btn.classList.add('active');
    } else {
      btn.classList.remove('active');
    }
  });

  // Load settings first to ensure we have the latest preferences
  loadSettings();

  // Apply Intelligent Mode badge after loading settings
  updateIntelligentModeBadge();

  // Update status to show we're in the chat tab
  updateStatus('Viewing chat');

  // Ensure the chat container is properly scrolled
  scrollChatToBottom();

  // Add a resize event listener to maintain proper scrolling
  window.addEventListener('resize', () => {
    // Debounce the resize event to avoid excessive scrolling
    if (window.resizeTimer) {
      clearTimeout(window.resizeTimer);
    }
    window.resizeTimer = setTimeout(() => {
      scrollChatToBottom();
    }, 100);
  });

  // Check if there's existing data and initialize RAG system automatically
  setTimeout(async () => {
    try {
      // Check if there's existing data
      const hasData = await ipcRenderer.invoke('check-existing-data');

      if (hasData) {
        addLog('Found existing data, initializing RAG system...');
        updateStatus('Initializing RAG system with existing data...');

        // If we're already on the RAG tab, update the status
        if (document.getElementById('rag-tab').classList.contains('active')) {
          ragStatus.textContent = 'Initializing RAG system with existing data...';
        }

        // Start chat with existing data
        await ipcRenderer.invoke('start-chat');

        // Set the RAG index as built
        ragIndexBuilt = true;
        crawlComplete = true;

        // Update status
        updateStatus('RAG system initialized');
        ragStatus.textContent = 'RAG system initialized. You can now ask questions.';

        // Clear any Intelligent Mode error status if Intelligent Mode is enabled
        if (userSettings.intelligentModeEnabled) {
          clearIntelligentModeError();
        }

        // Apply the user's preferred mode if Python process is running
        if (pythonProcessRunning) {
          applyUserPreferredMode()
            .then(() => {
              console.log('Applied user preferred mode after RAG initialization');
            })
            .catch(error => {
              console.error('Error applying user preferred mode after RAG initialization:', error);
            });
        }

        // Load sessions
        loadSessions();

        // Focus on the query input if we're on the RAG tab
        if (document.getElementById('rag-tab').classList.contains('active')) {
          queryInput.focus();
        }
      }
    } catch (error) {
      addLog(`Error checking for existing data: ${error.message}`);
    }
  }, 1000); // Wait 1 second after DOM is loaded to check for existing data
});

// Browse button for ChromeDriver
browseButton.addEventListener('click', async () => {
  const filePath = await ipcRenderer.invoke('select-file');
  if (filePath) {
    driverInput.value = filePath;
  }
});

// Start crawling
startButton.addEventListener('click', async () => {
  const url = urlInput.value.trim();
  if (!url) {
    alert('Please enter a valid URL');
    return;
  }

  try {
    // Switch to crawl tab
    switchToTab('crawl');

    // Clear previous logs
    logContainer.innerHTML = '';
    crawlProgress.style.width = '0%';

    // Update status
    updateStatus('Starting crawler...');

    // Get allowed paths from the textarea
    const allowedPathsInput = document.getElementById('allowed-paths-input');
    let allowedPaths = [];

    if (allowedPathsInput && allowedPathsInput.value.trim()) {
      // Split by newlines and filter out empty lines
      allowedPaths = allowedPathsInput.value
        .split('\n')
        .map(path => path.trim())
        .filter(path => path.length > 0);
    }

    // Start the crawl
    await ipcRenderer.invoke('start-crawl', {
      url: url,
      max_pages: parseInt(pagesInput.value),
      driver_path: driverInput.value.trim(),
      allowed_paths: allowedPaths
    });

  } catch (error) {
    addLog(`Error starting crawl: ${error.message}`);
    alert(`Error starting crawl: ${error.message}`);
  }
});

// Direct analysis button has been removed

// Mode badge element
const modeBadge = document.getElementById('mode-badge');

// Function to update the mode badge
function updateModeBadge(mode) {
  // Remove all existing classes
  modeBadge.classList.remove('retrieval-only', 'llm', 'deep-research');

  // Add the appropriate class
  modeBadge.classList.add(mode);

  // Update the text
  if (mode === 'retrieval-only') {
    modeBadge.textContent = 'Retrieval Only';
  } else if (mode === 'llm') {
    modeBadge.textContent = 'LLM Mode';
  } else if (mode === 'deep-research') {
    modeBadge.textContent = 'Deep Research';
  }
}

// Function to set the mode (updates UI and sends mode change to backend)
function setMode(mode) {
  // Update the mode badge
  updateModeBadge(mode);

  // Update the deep research toggle if needed
  if (mode === 'deep-research') {
    deepResearchToggle.checked = true;
    deepResearchOptions.classList.remove('hidden');
    queryInput.placeholder = 'Enter a research question to search the web...';
  } else {
    deepResearchToggle.checked = false;
    deepResearchOptions.classList.add('hidden');
    queryInput.placeholder = 'Ask a question about the website content...';
  }

  // If the RAG system is already initialized, send the mode change to the backend
  if (ragIndexBuilt && pythonProcessRunning) {
    try {
      // Send the mode change to the Python backend
      const useLLM = mode === 'llm';
      console.log(`Sending mode change to backend: useLLM=${useLLM}`);
      ipcRenderer.invoke('set-rag-mode', { useLLM: useLLM })
        .then(() => {
          // Update the status
          if (mode === 'llm') {
            ragStatus.textContent = 'LLM mode activated';
          } else if (mode === 'retrieval-only') {
            ragStatus.textContent = 'Retrieval-only mode activated';
          } else if (mode === 'deep-research') {
            ragStatus.textContent = 'Deep Research mode activated';
          }

          // Log the mode change
          addLog(`Switched to ${mode} mode`);
        })
        .catch(error => {
          console.error('Error changing RAG mode:', error);
        });
    } catch (error) {
      console.error('Error setting mode:', error);
    }
  }
}

// Deep Research mode toggle
deepResearchToggle.addEventListener('change', () => {
  const isEnabled = deepResearchToggle.checked;

  // Show/hide options
  if (isEnabled) {
    deepResearchOptions.classList.remove('hidden');
    updateModeBadge('deep-research');
    queryInput.placeholder = 'Enter a research question to search the web...';
  } else {
    deepResearchOptions.classList.add('hidden');
    // Restore previous mode
    const mode = userSettings.defaultAnalysisMethod === 'rag-only' ? 'retrieval-only' : 'llm';
    updateModeBadge(mode);
    queryInput.placeholder = 'Ask a question about the website content...';
  }

  // Save preference
  userSettings.deepResearchEnabled = isEnabled;
  saveSettings();
});

// Update search results count when changed
searchResultsCount.addEventListener('change', () => {
  userSettings.deepResearchResultsCount = parseInt(searchResultsCount.value);
  saveSettings();
});

// We no longer save research data to the database to avoid using cached results
// This ensures that each Deep Research query uses fresh data from the web

// Document Import Functionality
// Quick import button on setup tab
quickImportButton.addEventListener('click', async () => {
  try {
    const filePaths = await ipcRenderer.invoke('select-documents');
    if (filePaths && filePaths.length > 0) {
      // Store import options
      const importOptions = {
        analyzeAfterImport: analyzeAfterImportCheckbox.checked,
        storeOriginalPaths: storeOriginalPathsCheckbox.checked
      };

      // Update status
      updateStatus(`Importing ${filePaths.length} documents...`);

      // Import the documents with options
      const result = await ipcRenderer.invoke('import-documents', filePaths, importOptions);

      if (result.success) {
        updateStatus(`Successfully imported ${result.count} documents`);

        // If analyze after import is checked, switch to RAG tab
        if (importOptions.analyzeAfterImport) {
          // Wait a bit before switching to RAG tab
          setTimeout(() => {
            switchToTab('rag');
            ragStatus.textContent = 'Documents imported. You can now ask questions.';
          }, 1000);
        }
      } else {
        updateStatus(`Error importing documents: ${result.error}`);
      }
    }
  } catch (error) {
    console.error('Error importing documents:', error);
    updateStatus(`Error importing documents: ${error.message}`);
  }
});

// Advanced import button on setup tab
advancedImportButton.addEventListener('click', () => {
  // Switch to the import tab for advanced options
  switchToTab('import');
});

// Select documents button on import tab
selectDocumentsButton.addEventListener('click', async () => {
  try {
    const filePaths = await ipcRenderer.invoke('select-documents');
    if (filePaths && filePaths.length > 0) {
      addFilesToList(filePaths);
    }
  } catch (error) {
    console.error('Error selecting documents:', error);
    showImportStatus('error', `Error selecting documents: ${error.message}`);
  }
});

// Drag and drop functionality
dropZone.addEventListener('dragover', (e) => {
  e.preventDefault();
  dropZone.classList.add('drag-over');
});

dropZone.addEventListener('dragleave', () => {
  dropZone.classList.remove('drag-over');
});

dropZone.addEventListener('drop', (e) => {
  e.preventDefault();
  dropZone.classList.remove('drag-over');

  if (e.dataTransfer.files.length > 0) {
    const filePaths = Array.from(e.dataTransfer.files).map(file => file.path);
    addFilesToList(filePaths);
  }
});

// Click on drop zone to select files
dropZone.addEventListener('click', () => {
  selectDocumentsButton.click();
});

// Import documents button
importDocumentsButton.addEventListener('click', async () => {
  if (selectedFiles.length === 0) {
    showImportStatus('warning', 'No files selected for import');
    return;
  }

  try {
    // Update status
    showImportStatus('info', `Importing ${selectedFiles.length} documents...`);
    updateStatus(`Importing ${selectedFiles.length} documents...`);

    // Disable buttons during import
    importDocumentsButton.disabled = true;
    clearFilesButton.disabled = true;
    selectDocumentsButton.disabled = true;

    // Get import options from the main settings
    const importOptions = {
      storeOriginalPaths: storeOriginalPathsCheckbox.checked,
      analyzeAfterImport: analyzeAfterImportCheckbox.checked
    };

    // Import the documents with options
    const result = await ipcRenderer.invoke('import-documents', selectedFiles, importOptions);

    if (result.success) {
      showImportStatus('success', `Successfully imported ${result.count} documents`);
      updateStatus(`Imported ${result.count} documents`);

      // Clear the file list
      clearFileList();

      // Display the imported documents
      displayImportedDocuments(result.documents);
    } else {
      showImportStatus('error', `Error importing documents: ${result.error}`);
    }
  } catch (error) {
    console.error('Error importing documents:', error);
    showImportStatus('error', `Error importing documents: ${error.message}`);
  } finally {
    // Re-enable buttons
    importDocumentsButton.disabled = false;
    clearFilesButton.disabled = false;
    selectDocumentsButton.disabled = false;
  }
});

// Clear files button
clearFilesButton.addEventListener('click', () => {
  clearFileList();
});

// Helper function to add files to the list
function addFilesToList(filePaths) {
  // Add files to the selected files array
  filePaths.forEach(filePath => {
    // Check if the file is already in the list
    if (!selectedFiles.includes(filePath)) {
      selectedFiles.push(filePath);
    }
  });

  // Update the file list UI
  updateFileListUI();
}

// Helper function to update the file list UI
function updateFileListUI() {
  // Clear the file list
  fileList.innerHTML = '';

  // Show the file list if there are files
  if (selectedFiles.length > 0) {
    fileList.classList.remove('hidden');
    importDocumentsButton.disabled = false;
  } else {
    fileList.classList.add('hidden');
    importDocumentsButton.disabled = true;
    return;
  }

  // Add each file to the list
  selectedFiles.forEach((filePath, index) => {
    const fileName = filePath.split(/[\\/]/).pop();
    const fileExt = fileName.split('.').pop().toLowerCase();

    // Create file item element
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';

    // Determine file icon based on extension
    let fileIconClass = 'file-type-txt';
    if (['pdf'].includes(fileExt)) {
      fileIconClass = 'file-type-pdf';
    } else if (['docx', 'doc'].includes(fileExt)) {
      fileIconClass = 'file-type-docx';
    } else if (['csv'].includes(fileExt)) {
      fileIconClass = 'file-type-csv';
    } else if (['xlsx', 'xls'].includes(fileExt)) {
      fileIconClass = 'file-type-xlsx';
    }

    // Create file item content
    fileItem.innerHTML = `
      <div class="file-icon ${fileIconClass}">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
      </div>
      <div class="file-info">
        <div class="file-name">${fileName}</div>
        <div class="file-meta">${fileExt.toUpperCase()} file</div>
      </div>
      <div class="file-remove" data-index="${index}">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
      </div>
    `;

    // Add file item to the list
    fileList.appendChild(fileItem);

    // Add event listener to remove button
    const removeButton = fileItem.querySelector('.file-remove');
    removeButton.addEventListener('click', (e) => {
      e.stopPropagation();
      const index = parseInt(removeButton.getAttribute('data-index'));
      selectedFiles.splice(index, 1);
      updateFileListUI();
    });
  });
}

// Helper function to clear the file list
function clearFileList() {
  selectedFiles = [];
  fileList.innerHTML = '';
  fileList.classList.add('hidden');
  importDocumentsButton.disabled = true;
}

// Helper function to show import status
function showImportStatus(type, message) {
  importStatus.innerHTML = '';
  importStatus.classList.remove('hidden');

  let iconName = 'info';
  let iconClass = '';

  if (type === 'success') {
    iconName = 'check-circle';
    iconClass = 'success';
  } else if (type === 'error') {
    iconName = 'alert-circle';
    iconClass = 'error';
  } else if (type === 'warning') {
    iconName = 'alert-triangle';
    iconClass = 'warning';
  }

  importStatus.innerHTML = `
    <div class="status-icon ${iconClass}">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>
    </div>
    <div class="status-text">${message}</div>
  `;

  // Auto-hide success and info messages after 5 seconds
  if (type === 'success' || type === 'info') {
    setTimeout(() => {
      importStatus.classList.add('hidden');
    }, 5000);
  }
}

// Helper function to display imported documents
function displayImportedDocuments(documents) {
  if (!documents || documents.length === 0) {
    importedDocuments.classList.add('hidden');
    return;
  }

  // Show the imported documents section
  importedDocuments.classList.remove('hidden');

  // Clear the document grid
  documentGrid.innerHTML = '';

  // Add each document to the grid
  documents.forEach(doc => {
    const docCard = document.createElement('div');
    docCard.className = 'document-card';

    // Determine document icon based on type
    let docIconClass = 'file-type-txt';
    if (doc.type === 'pdf') {
      docIconClass = 'file-type-pdf';
    } else if (['docx', 'doc'].includes(doc.type)) {
      docIconClass = 'file-type-docx';
    } else if (doc.type === 'csv') {
      docIconClass = 'file-type-csv';
    } else if (['xlsx', 'xls'].includes(doc.type)) {
      docIconClass = 'file-type-xlsx';
    }

    // Create document card content
    docCard.innerHTML = `
      <div class="document-icon ${docIconClass}">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
      </div>
      <div class="document-title" title="${doc.filename}">${doc.filename}</div>
      <div class="document-meta">${doc.type.toUpperCase()} - ID: ${doc.id}</div>
    `;

    // Add document card to the grid
    documentGrid.appendChild(docCard);
  });
}

// Listen for documents-imported event
ipcRenderer.on('documents-imported', (event, data) => {
  if (data.count > 0) {
    showImportStatus('success', `Successfully imported ${data.count} documents`);
    displayImportedDocuments(data.documents);
  }
});

// RAG analysis with LLM
ragAnalysisButton.addEventListener('click', async () => {
  if (!crawlComplete) {
    alert('Please complete a crawl first before using RAG.');
    return;
  }

  try {
    // Switch to RAG tab
    switchToTab('rag');

    // Clear previous results
    answerContainer.innerHTML = '';
    ragStatus.textContent = 'Building RAG index with LLM...';

    // Update mode badge
    updateModeBadge('llm');

    // Update status
    updateStatus('Building RAG index with LLM...');

    // Start RAG with LLM
    await ipcRenderer.invoke('start-rag', { useLLM: true });

    // Update state
    ragIndexBuilt = true;

  } catch (error) {
    addLog(`Error building RAG index: ${error.message}`);
    ragStatus.textContent = `Error: ${error.message}`;
  }
});

// RAG analysis without LLM (retrieval only)
ragOnlyAnalysisButton.addEventListener('click', async () => {
  if (!crawlComplete) {
    alert('Please complete a crawl first before using RAG.');
    return;
  }

  try {
    // Switch to RAG tab
    switchToTab('rag');

    // Clear previous results
    answerContainer.innerHTML = '';
    ragStatus.textContent = 'Building RAG index (retrieval only)...';

    // Update mode badge
    updateModeBadge('retrieval-only');

    // Update status
    updateStatus('Building RAG index (retrieval only)...');

    // Start RAG without LLM
    await ipcRenderer.invoke('start-rag', { useLLM: false });

    // Update state
    ragIndexBuilt = true;

  } catch (error) {
    addLog(`Error building RAG index: ${error.message}`);
    ragStatus.textContent = `Error: ${error.message}`;
  }
});

// File attachment in chat
attachFileButton.addEventListener('click', async () => {
  try {
    // If a file is already attached, remove it first
    if (attachedFile) {
      removeAttachedFile();
      return;
    }

    // Select a document file
    const filePaths = await ipcRenderer.invoke('select-documents');
    if (filePaths && filePaths.length > 0) {
      // Use only the first file
      const filePath = filePaths[0];

      // Get file info
      const fs = require('fs');
      const path = require('path');
      const stats = fs.statSync(filePath);

      // Store file info
      attachedFile = {
        path: filePath,
        name: path.basename(filePath),
        size: stats.size,
        type: path.extname(filePath).toLowerCase().substring(1)
      };

      // Format file size
      const formattedSize = formatFileSize(attachedFile.size);

      // Update UI
      attachedFilename.textContent = attachedFile.name;
      attachedFilesize.textContent = `(${formattedSize})`;
      attachedFilePreview.classList.remove('hidden');

      // Change the attach button appearance
      attachFileButton.innerHTML = `
        <div class="button-icon-wrapper">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>`;
      attachFileButton.title = "Remove attached file";

      // Focus on the query input
      queryInput.focus();
    }
  } catch (error) {
    console.error('Error attaching file:', error);
  }
});

// Remove attached file
removeFileButton.addEventListener('click', () => {
  removeAttachedFile();
});

// Helper function to remove attached file
function removeAttachedFile() {
  attachedFile = null;
  attachedFilePreview.classList.add('hidden');

  // Reset the attach button appearance
  attachFileButton.innerHTML = `
    <div class="button-icon-wrapper">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
      </svg>
    </div>`;
  attachFileButton.title = "Attach a document";
}

// Helper function to format file size
function formatFileSize(bytes) {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(1) + ' KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  } else {
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  }
}

// Handle Intelligent Mode query
async function handleIntelligentModeQuery(query) {
  try {
    // Disable input while processing
    queryInput.disabled = true;
    queryButton.disabled = true;

    // Add user message to chat - only add to UI, not to database yet
    // The backend will handle adding to database to prevent duplication
    const userMessage = addChatMessage('user', `<p>${query}</p>`);

    // Ensure the user message is visible with important styling
    userMessage.style.display = 'block';
    userMessage.style.opacity = '1';
    userMessage.style.visibility = 'visible';

    // Force a reflow to ensure the message is displayed
    void userMessage.offsetWidth;

    // Create a new assistant message
    currentAssistantMessage = addChatMessage('assistant', '');

    // Add an enhanced processing indicator
    const processingDiv = document.createElement('div');
    processingDiv.className = 'processing';

    processingDiv.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12 6 12 12 16 14"></polyline>
      </svg>
      Processing with Intelligent Mode
      <div class="typing-indicator"><span></span><span></span><span></span></div>
    `;

    answerContainer.appendChild(processingDiv);

    // Store the processing indicator to remove it later
    currentAssistantMessage.processingIndicator = processingDiv;

    // Ensure proper scrolling
    scrollChatToBottom();

    // Update status
    updateStatus('Processing with Intelligent Mode...');
    ragStatus.textContent = 'Analyzing request and selecting appropriate tools...';

    console.log('Sending intelligent query:', query);

    // Send the query to the intelligent query handler
    const result = await ipcRenderer.invoke('intelligent-query', {
      query: query,
      model: modelSelect.value,
      k: 3
    });

    console.log('Received intelligent query result:', result);

    // Remove the processing indicator
    if (currentAssistantMessage.processingIndicator) {
      currentAssistantMessage.processingIndicator.remove();
      delete currentAssistantMessage.processingIndicator;
    }

    // Format and display the result
    const formattedResult = formatIntelligentResponse(result.result);
    currentAssistantMessage.innerHTML = formattedResult;

    // The backend already stores the assistant's response in the conversation manager
    // No need to store it again here to prevent duplication

    // Re-enable input
    queryInput.disabled = false;
    queryButton.disabled = false;

    // Update status
    ragStatus.textContent = 'Intelligent Mode processing complete';
    updateStatus('Processing complete');

    // Ensure proper scrolling after content is added
    scrollChatToBottom();

    // Focus on the input field
    queryInput.focus();
  } catch (error) {
    console.error('Error in Intelligent Mode query:', error);

    // Remove the processing indicator if it exists
    if (currentAssistantMessage && currentAssistantMessage.processingIndicator) {
      currentAssistantMessage.processingIndicator.remove();
      delete currentAssistantMessage.processingIndicator;
    }

    // Add error message to the current assistant message
    if (currentAssistantMessage) {
      currentAssistantMessage.innerHTML = `<p class="error">Error: ${error.message}</p>`;
    } else {
      // Fallback to adding a new system message if currentAssistantMessage is not available
      addChatMessage('system', `<p class="error">Error: ${error.message}</p>`);
    }

    // Re-enable input
    queryInput.disabled = false;
    queryButton.disabled = false;

    // Update status
    ragStatus.textContent = 'Error in Intelligent Mode';
    updateStatus('Ready');

    // Ensure proper scrolling after error message is added
    scrollChatToBottom();
  }
}

// Function to format markdown text for display
function formatMarkdown(text) {
  if (!text) return '';

  // Clean up the text - remove any "A:" or "Answer:" prefixes
  let cleanText = text;
  if (cleanText.startsWith('A:')) {
    cleanText = cleanText.substring(2).trim();
  } else if (cleanText.startsWith('Answer:')) {
    cleanText = cleanText.substring(7).trim();
  }

  // Format headings
  cleanText = cleanText.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, content) => {
    const level = hashes.length;
    return `<h${level} class="content-heading">${content}</h${level}>`;
  });

  // Format bold text
  cleanText = cleanText.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
  cleanText = cleanText.replace(/__(.+?)__/g, '<strong>$1</strong>');

  // Format italic text
  cleanText = cleanText.replace(/\*([^\*]+?)\*/g, '<em>$1</em>');
  cleanText = cleanText.replace(/_([^_]+?)_/g, '<em>$1</em>');

  // Format unordered lists
  const ulRegex = /((?:^|\n)(?:- |\* |• ).+(?:\n(?:- |\* |• ).+)*)/g;
  cleanText = cleanText.replace(ulRegex, function(match) {
    const listItems = match.split('\n').filter(line => {
      const trimmed = line.trim();
      return trimmed.startsWith('- ') || trimmed.startsWith('* ') || trimmed.startsWith('• ');
    });
    const formattedItems = listItems.map(item => {
      let content = item.replace(/^-\s+|\*\s+|•\s+/, '').trim();
      content = content.replace(/(https?:\/\/[^\s\)]+)/g, '<a href="$1" target="_blank">$1</a>');
      return `<li>${content}</li>`;
    }).join('');
    return `<ul class="markdown-list">${formattedItems}</ul>`;
  });

  // Format ordered lists
  const olRegex = /((?:^|\n)(?:\d+\. ).+(?:\n(?:\d+\. ).+)*)/g;
  cleanText = cleanText.replace(olRegex, function(match) {
    const listItems = match.split('\n').filter(line => /^\d+\.\s+/.test(line.trim()));
    const formattedItems = listItems.map(item => {
      let content = item.replace(/^\d+\.\s+/, '').trim();
      content = content.replace(/(https?:\/\/[^\s\)]+)/g, '<a href="$1" target="_blank">$1</a>');
      return `<li>${content}</li>`;
    }).join('');
    return `<ol class="markdown-list">${formattedItems}</ol>`;
  });

  // Format blockquotes
  cleanText = cleanText.replace(/^>\s+(.+)$/gm, (match, content) => {
    return `<blockquote class="markdown-quote">${content}</blockquote>`;
  });

  // Convert URLs to clickable links
  cleanText = cleanText.replace(
    /(?<!href="|">)(https?:\/\/[^\s\)<]+)/g,
    '<a href="$1" target="_blank" class="content-link">$1</a>'
  );

  // Format paragraphs and line breaks
  cleanText = cleanText
    .split('\n\n')
    .map(paragraph => {
      // Skip paragraphs that are already formatted as HTML
      if (paragraph.trim().startsWith('<') &&
          (paragraph.includes('</') || paragraph.includes('/>'))) {
        return paragraph;
      }
      // Skip empty paragraphs
      if (paragraph.trim() === '') {
        return '';
      }
      return `<p class="content-paragraph">${paragraph.replace(/\n/g, '<br>')}</p>`;
    })
    .join('');

  return cleanText;
}

// Format the intelligent mode response
function formatIntelligentResponse(result) {
  if (!result) {
    return '<p>No response received from Intelligent Mode.</p>';
  }

  let formattedResponse = '';

  // Only show debugging information if in development mode
  const isDevMode = false; // Set to false to hide debugging info

  if (isDevMode) {
    // Add the reasoning section
    if (result.reasoning) {
      formattedResponse += `<div class="intelligent-reasoning">
        <h3>🧠 Reasoning</h3>
        <p>${result.reasoning}</p>
      </div>`;
    }

    // Add the selected tool section
    if (result.selectedTool && result.selectedTool !== 'none') {
      formattedResponse += `<div class="intelligent-tool">
        <h3>🛠️ Selected Tool: ${result.selectedTool}</h3>
      </div>`;

      // Add the execution plan if available
      if (result.executionPlan) {
        formattedResponse += `<div class="intelligent-plan">
          <h4>Execution Plan:</h4>
          <p>${result.executionPlan}</p>
        </div>`;
      }
    }
  }

  // Check if we have a result object
  if (result.result) {
    // Add the result content based on type
    if (result.result.type === 'direct_response') {
      formattedResponse += `<div class="intelligent-response">
        <h3>Response</h3>
        <div class="response-content">${result.result.content}</div>
      </div>`;
    }
    else if (result.result.type === 'deep_research') {
      // Format the deep research content with markdown formatting
      const formattedContent = formatMarkdown(result.result.content);

      formattedResponse += `<div class="intelligent-response">
        <h3>Research Results</h3>
        <div class="response-content">${formattedContent}</div>

        <h4>Sources:</h4>
        <ul class="source-list">
          ${result.result.sources.map(source => `<li><a href="${source.url}" target="_blank">${source.title}</a></li>`).join('')}
        </ul>
      </div>`;
    }
    else if (result.result.type === 'rag_query') {
      formattedResponse += `<div class="intelligent-response">
        <h3>RAG Query Results</h3>
        <div class="response-content">${result.result.content}</div>
      </div>`;
    }
    else if (result.result.type === 'instruction') {
      formattedResponse += `<div class="intelligent-instruction">
        <h3>Instructions</h3>
        <p>${result.result.content}</p>
      </div>`;
    }
    else if (result.result.type === 'error') {
      formattedResponse += `<div class="intelligent-error">
        <h3>Error</h3>
        <p>${result.result.content}</p>
      </div>`;
    }
    else if (result.result.type === 'tool_error') {
      formattedResponse += `<div class="intelligent-error">
        <h3>Tool Selection Error</h3>
        <div class="response-content">${result.result.content}</div>
        <p>Would you like me to try a different approach?</p>
      </div>`;
    }
    else if (result.result.type === 'pinecone_config') {
      formattedResponse += `<div class="intelligent-success">
        <h3>Configuration Complete</h3>
        <p>${result.result.content}</p>
      </div>`;
    }
  } else if (result.type) {
    // For backward compatibility with the old format
    // Add the result content based on type
    if (result.type === 'direct_response') {
      formattedResponse += `<div class="intelligent-response">
        <h3>Response</h3>
        <div class="response-content">${result.content}</div>
      </div>`;
    }
    else if (result.type === 'deep_research') {
      // Format the deep research content with markdown formatting
      const formattedContent = formatMarkdown(result.content);

      formattedResponse += `<div class="intelligent-response">
        <h3>Research Results</h3>
        <div class="response-content">${formattedContent}</div>

        <h4>Sources:</h4>
        <ul class="source-list">
          ${result.sources.map(source => `<li><a href="${source.url}" target="_blank">${source.title}</a></li>`).join('')}
        </ul>
      </div>`;
    }
    else if (result.type === 'rag_query') {
      formattedResponse += `<div class="intelligent-response">
        <h3>RAG Query Results</h3>
        <div class="response-content">${result.content}</div>
      </div>`;
    }
    else if (result.type === 'instruction') {
      formattedResponse += `<div class="intelligent-instruction">
        <h3>Instructions</h3>
        <p>${result.content}</p>
      </div>`;
    }
    else if (result.type === 'error') {
      formattedResponse += `<div class="intelligent-error">
        <h3>Error</h3>
        <p>${result.content}</p>
      </div>`;
    }
    else if (result.type === 'tool_error') {
      formattedResponse += `<div class="intelligent-error">
        <h3>Tool Selection Error</h3>
        <div class="response-content">${result.content}</div>
        <p>Would you like me to try a different approach?</p>
      </div>`;
    }
    else if (result.type === 'pinecone_config') {
      formattedResponse += `<div class="intelligent-success">
        <h3>Configuration Complete</h3>
        <p>${result.content}</p>
      </div>`;
    }
  } else {
    // If no result type is found, show a generic message
    formattedResponse += `<div class="intelligent-error">
      <h3>Error</h3>
      <p>No result data found in the response.</p>
    </div>`;
  }

  return formattedResponse;
}

// Format tools response with better styling
function formatToolsResponse(response) {
  // Extract the answer prefix if present
  let formattedResponse = response;
  if (response.startsWith('Answer:')) {
    formattedResponse = response.substring(7).trim();
  }

  // Apply formatting to tool and procedure sections
  formattedResponse = formattedResponse.replace(/TOOL: ([^\n]+)/g, '<h3 class="tool-heading">🛠️ $1</h3>');
  formattedResponse = formattedResponse.replace(/PROCEDURE: ([^\n]+)/g, '<h3 class="procedure-heading">📋 $1</h3>');

  // Format description, usage, and examples sections
  formattedResponse = formattedResponse.replace(/DESCRIPTION: ([^\n]+)/g, '<p class="tool-description"><strong>Description:</strong> $1</p>');
  formattedResponse = formattedResponse.replace(/USAGE: ([^EXAMPLES:]+)/gs, '<div class="tool-usage"><strong>Usage:</strong><p>$1</p></div>');
  formattedResponse = formattedResponse.replace(/WHEN TO USE: ([^\n]+)/g, '<p class="tool-context"><strong>When to use:</strong> $1</p>');

  // Format examples section
  formattedResponse = formattedResponse.replace(/EXAMPLES:\n([\s\S]*?)(?=\n\n|$)/g, (match, examples) => {
    const formattedExamples = examples.replace(/- (.+)/g, '<li>$1</li>');
    return `<div class="tool-examples"><strong>Examples:</strong><ul>${formattedExamples}</ul></div>`;
  });

  // Format steps section
  formattedResponse = formattedResponse.replace(/STEPS:\n([\s\S]*?)(?=\n\n|$)/g, (match, steps) => {
    const formattedSteps = steps.replace(/(\d+)\. (.+)/g, '<li>$2</li>');
    return `<div class="tool-steps"><strong>Steps:</strong><ol>${formattedSteps}</ol></div>`;
  });

  // Format tags section
  formattedResponse = formattedResponse.replace(/TAGS: ([^\n]+)/g, '<p class="tool-tags"><strong>Tags:</strong> <span class="tags">$1</span></p>');

  // Format relevance score
  formattedResponse = formattedResponse.replace(/Relevance: ([0-9.]+)/g, '<p class="relevance-score"><strong>Relevance:</strong> <span class="score">$1</span></p>');

  // Format separators
  formattedResponse = formattedResponse.replace(/---/g, '<hr class="tool-separator">');

  return formattedResponse;
}

// RAG query
queryButton.addEventListener('click', async () => {
  const query = queryInput.value.trim();
  if (!query) {
    alert('Please enter a question');
    return;
  }

  // Check if we're in Intelligent Mode
  if (userSettings.intelligentModeEnabled) {
    // Handle Intelligent Mode query
    await handleIntelligentModeQuery(query);
    return;
  }

  // If RAG index is not built but we have existing data, try to build it
  if (!ragIndexBuilt) {
    // Check if we're analyzing existing data
    if (document.getElementById('existing-data-notice')) {
      // Set the flag to true since we're working with existing data
      ragIndexBuilt = true;
      ragStatus.textContent = 'Using existing database data...';

      // We need to start the Python process with existing data first
      try {
        addLog('Starting analysis of existing data for RAG...');
        updateStatus('Starting RAG with existing data...');

        // Start the Python process with existing data
        await ipcRenderer.invoke('analyze-existing', {
          method: 'rag',
          skipPrompt: true  // Skip the analysis choice prompt
        });

        // Wait a bit for the process to initialize
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        addLog(`Error initializing RAG: ${error.message}`);
        ragStatus.textContent = `Error: ${error.message}`;
        return;
      }
    } else {
      alert('RAG index is not yet built. Please wait.');
      return;
    }
  }

  try {
    // Disable input while processing
    queryInput.disabled = true;
    queryButton.disabled = true;

    // Update status
    updateStatus('Processing query...');
    ragStatus.textContent = 'Processing query...';

    // First add the user's question as a message with proper styling
    const userMessage = addChatMessage('user', `<p>${query}</p>`);

    // Ensure the user message is visible with important styling
    userMessage.style.display = 'block';
    userMessage.style.opacity = '1';
    userMessage.style.visibility = 'visible';

    // Force a reflow to ensure the message is displayed
    void userMessage.offsetWidth;

    // Create a new assistant message with proper styling
    currentAssistantMessage = addChatMessage('assistant', '');
    messageBuffer = '';

    // Ensure the message is visible with important styling
    currentAssistantMessage.style.display = 'block';
    currentAssistantMessage.style.opacity = '1';
    currentAssistantMessage.style.visibility = 'visible';
    currentAssistantMessage.style.zIndex = '2';

    // Add an enhanced processing indicator
    const processingDiv = document.createElement('div');
    processingDiv.className = 'processing';
    processingDiv.style.display = 'block';
    processingDiv.style.opacity = '1';
    processingDiv.style.visibility = 'visible';
    processingDiv.style.zIndex = '1';

    // Get the current mode
    const currentMode = modeBadge.classList.contains('llm') ? 'LLM' :
                        modeBadge.classList.contains('deep-research') ? 'Deep Research' :
                        'Retrieval';

    // Create a more informative processing message based on the mode
    let processingMessage = '';
    if (attachedFile) {
      processingMessage = `Analyzing document with ${currentMode} mode`;
    } else if (modeBadge.classList.contains('deep-research')) {
      processingMessage = 'Searching the web and analyzing results';
    } else if (modeBadge.classList.contains('llm')) {
      processingMessage = 'Processing with AI';
    } else {
      processingMessage = 'Retrieving relevant information';
    }

    processingDiv.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12 6 12 12 16 14"></polyline>
      </svg>
      ${processingMessage}
      <div class="typing-indicator"><span></span><span></span><span></span></div>
    `;

    answerContainer.appendChild(processingDiv);

    // Store the processing indicator to remove it later
    currentAssistantMessage.processingIndicator = processingDiv;

    // Ensure proper scrolling
    scrollChatToBottom();

    // Check if we have an attached file
    if (attachedFile) {
      // Process the attached file first
      console.log(`Processing attached file: ${attachedFile.name}`);
      updateStatus(`Processing attached file: ${attachedFile.name}...`);

      try {
        // Import the file
        const importResult = await ipcRenderer.invoke('import-documents', [attachedFile.path], {
          storeOriginalPaths: true,
          analyzeAfterImport: false,
          temporaryImport: true // Mark as temporary so it can be cleaned up later
        });

        if (!importResult.success) {
          throw new Error(`Failed to process attached file: ${importResult.error || 'Unknown error'}`);
        }

        // Get the document ID
        const docId = importResult.documents[0]?.id;
        if (!docId) {
          throw new Error('Failed to get document ID after import');
        }

        // Check if we're in Deep Research mode
        const isDeepResearchMode = modeBadge.classList.contains('deep-research');

        if (isDeepResearchMode) {
          // Deep Research mode with document context
          console.log('Sending query in Deep Research mode with document context');

          // Get the number of results to analyze
          const resultsCount = parseInt(searchResultsCount.value);

          // Send query with Deep Research parameters and document context
          await ipcRenderer.invoke('deep-research', {
            query: query,
            model: modelSelect.value,
            resultsCount: resultsCount,
            saveToDatabase: false, // Always false to ensure fresh results
            documentContext: {
              documentId: docId,
              fileName: attachedFile.name
            }
          });
        } else {
          // Regular RAG mode with document context
          const currentMode = modeBadge.classList.contains('llm') ? 'llm' : 'retrieval-only';
          const useLLM = currentMode === 'llm';

          console.log(`Sending query in ${currentMode} mode with document context (useLLM: ${useLLM})`);

          // Send query with the appropriate mode and document context
          await ipcRenderer.invoke('query-rag', {
            query: query,
            model: modelSelect.value,
            needsModel: waitingForModelSelection,
            useLLM: useLLM,
            documentContext: {
              documentId: docId,
              fileName: attachedFile.name
            }
          });
        }

        // Remove the attached file from UI after processing
        removeAttachedFile();

      } catch (error) {
        console.error('Error processing attached file:', error);
        throw error; // Re-throw to be caught by the outer try-catch
      }
    } else {
      // No attached file - normal processing

      // Check if we're in Deep Research mode
      const isDeepResearchMode = modeBadge.classList.contains('deep-research');

      if (isDeepResearchMode) {
        // Deep Research mode - search the web and analyze results
        console.log('Sending query in Deep Research mode');

        // Get the number of results to analyze
        const resultsCount = parseInt(searchResultsCount.value);

        // Send query with Deep Research parameters
        // We always set saveToDatabase to false to ensure fresh results each time
        await ipcRenderer.invoke('deep-research', {
          query: query,
          model: modelSelect.value,
          resultsCount: resultsCount,
          saveToDatabase: false // Always false to ensure fresh results
        });
      } else {
        // Regular RAG mode
        // Check if we're in LLM mode or retrieval-only mode
        const currentMode = modeBadge.classList.contains('llm') ? 'llm' : 'retrieval-only';
        const useLLM = currentMode === 'llm';

        console.log(`Sending query in ${currentMode} mode (useLLM: ${useLLM})`);

        // Send query with the appropriate mode
        await ipcRenderer.invoke('query-rag', {
          query: query,
          model: modelSelect.value,
          needsModel: waitingForModelSelection,
          useLLM: useLLM
        });
      }
    }

    // Reset flag
    waitingForModelSelection = false;

  } catch (error) {
    addLog(`Error processing query: ${error.message}`);

    // Add error message to chat
    const errorMessage = addChatMessage('system', '');
    errorMessage.innerHTML = `<p>Error: ${error.message}</p>`;

    // Re-enable input on error
    queryInput.disabled = false;
    queryButton.disabled = false;

    // Update status
    ragStatus.textContent = 'Error processing query.';
    updateStatus('Error');
  }

  // Clear the input field after sending the query
  queryInput.value = '';
});

// Helper functions
function switchToTab(tabName) {
  // Get all nav buttons
  const navButtons = document.querySelectorAll('.nav-button');

  // Find and click the button with the matching tab name
  navButtons.forEach(btn => {
    if (btn.getAttribute('data-tab') === tabName) {
      btn.click();
    }
  });
}

function updateStatus(status) {
  // Don't show Python process errors in the status text
  if (status && (status.includes('Python process is not running') || status.includes('Python process not available'))) {
    console.log('Suppressing Python process error in status text:', status);
    return;
  }

  statusText.textContent = status;
  statusText.setAttribute('data-text', status);
  crawlStatus.textContent = status;
}

function addLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  const logEntry = `[${timestamp}] ${message}`;
  logContainer.innerHTML += logEntry + '\n';
  logContainer.scrollTop = logContainer.scrollHeight;
  console.log(logEntry);
}

// Listen for Python output
ipcRenderer.on('python-output', (event, output) => {
  // Add to log
  addLog(output);

  // Handle ChromeDriver prompt specifically
  if (output.includes('Enter path to ChromeDriver') ||
      output.includes('press Enter to use automatic download')) {
    updateStatus('Configuring ChromeDriver...');
  }

  // Update results if in analysis mode
  if (output.includes('Analysis Results') ||
      (output.includes('A:') && !output.includes('Q:')) ||
      output.includes('Here is my analysis')) {
    resultsContainer.innerHTML += output + '\n';
    resultsContainer.scrollTop = resultsContainer.scrollHeight;
  }

  // We're now handling RAG answers through the rag-partial-answer and rag-answer events
  // This old code is removed to avoid conflicts with the new chat message format

  // Check for crawl completion
  if (output.includes('Crawl complete') || output.includes('Processing complete')) {
    crawlComplete = true;
    analysisStatus.textContent = 'Crawl complete. Choose an analysis method:';
    updateStatus('Ready for analysis');
  }

  // Check for crawl progress indicators
  if (output.includes('Crawling') || output.includes('Fetching page')) {
    updateStatus('Crawling in progress...');
  }

  // Handle Pinecone-related messages
  if (output.includes('Pinecone connection successful')) {
    pineconeStatus.textContent = 'Connection successful! Pinecone is properly configured.';
    pineconeStatus.className = 'success';
  } else if (output.includes('Successfully connected to Pinecone index')) {
    pineconeStatus.textContent = 'Successfully connected to Pinecone! Index is properly configured.';
    pineconeStatus.className = 'success';
  } else if (output.includes('Pinecone configuration successful')) {
    pineconeStatus.textContent = 'Pinecone configuration successful!';
    pineconeStatus.className = 'success';
  } else if (output.includes('Error initializing Pinecone')) {
    pineconeStatus.textContent = 'Error connecting to Pinecone: ' + output.split('Error initializing Pinecone:')[1].trim();
    pineconeStatus.className = 'error';
  } else if (output.includes('Successfully uploaded') && output.includes('vectors to Pinecone')) {
    pineconeStatus.textContent = output.trim();
    pineconeStatus.className = 'success';
  } else if (output.includes('Index stats:')) {
    // Format and display the index stats
    pineconeStatus.innerHTML = 'Pinecone Index Stats:<br>' +
      output.replace('Index stats:', '').trim().replace(/\n/g, '<br>');
    pineconeStatus.className = 'success';

    // Remove progress bar if it exists
    const progressContainer = pineconeStatus.querySelector('.upload-progress-container');
    if (progressContainer) {
      progressContainer.remove();
    }
  } else if (output.includes('Uploaded') && output.includes('vectors...')) {
    // Extract progress information
    const match = output.match(/Uploaded (\d+)\/(\d+) vectors/);
    if (match && match[1] && match[2]) {
      const current = parseInt(match[1]);
      const total = parseInt(match[2]);
      const percent = Math.round((current / total) * 100);

      // Update progress bar
      const progressBar = pineconeStatus.querySelector('.upload-progress-bar');
      if (progressBar) {
        progressBar.style.width = `${percent}%`;
      }

      // Update status text
      pineconeStatus.textContent = `Uploading vectors to Pinecone: ${current}/${total} (${percent}%)`;
      pineconeStatus.className = 'info';
    }
  } else if (output.includes('Switched to Pinecone vector store')) {
    updateCloudStatus(true);
  } else if (output.includes('Switched to local FAISS vector store')) {
    updateCloudStatus(false);
  }

  // Check for RAG index completion
  if (output.includes('RAG index built') ||
      output.includes('FAISS index built') ||
      output.includes('Setting up RAG system') ||
      output.includes('Loaded') && output.includes('pages from the database')) {

    // If the index is being built, update the status
    if (output.includes('Setting up RAG system')) {
      ragStatus.textContent = 'Building RAG index...';
      updateStatus('Building RAG index...');
    }
    // If the index is built, update the status
    else if (output.includes('RAG index built') || output.includes('FAISS index built')) {
      ragIndexBuilt = true;
      ragStatus.textContent = 'RAG index built. You can now ask questions about the content.';
      updateStatus('RAG ready');
      queryInput.focus();

      // Remove the progress bar if it exists
      const progressBar = document.getElementById('rag-progress-bar');
      if (progressBar && progressBar.parentNode) {
        progressBar.parentNode.remove();
      }

      // Apply the user's preferred mode if Python process is running
      if (pythonProcessRunning) {
        applyUserPreferredMode()
          .then(() => {
            console.log('Applied user preferred mode after RAG index built');
          })
          .catch(error => {
            console.error('Error applying user preferred mode after RAG index built:', error);
          });
      }
    }
    // If data is loaded from the database, mark the RAG index as built
    else if (output.includes('Loaded') && output.includes('pages from the database')) {
      ragIndexBuilt = true;
      ragStatus.textContent = 'Database loaded. You can now ask questions about the content.';
      updateStatus('RAG ready');
      queryInput.focus();

      // Apply the user's preferred mode if Python process is running
      if (pythonProcessRunning) {
        applyUserPreferredMode()
          .then(() => {
            console.log('Applied user preferred mode after database loaded');
          })
          .catch(error => {
            console.error('Error applying user preferred mode after database loaded:', error);
          });
      }
    }
  }

  // Check if waiting for model selection
  if (output.includes('Select a model:')) {
    waitingForModelSelection = true;
    updateStatus('Waiting for model selection...');
  }
});

// Listen for Python errors
ipcRenderer.on('python-error', (event, error) => {
  addLog(`ERROR: ${error}`);
});

// Listen for Python progress bar updates
ipcRenderer.on('python-progress', (event, progressText) => {
  // Don't log every progress bar update to avoid cluttering the log
  // Just update the status
  updateStatus('Building RAG index...');
});

// Listen for RAG progress percentage updates
ipcRenderer.on('rag-progress', (event, percent) => {
  // Update the progress bar if we're on the RAG tab
  if (document.getElementById('rag-tab').classList.contains('active')) {
    // Create a progress bar if it doesn't exist
    let progressBar = document.getElementById('rag-progress-bar');
    if (!progressBar) {
      // Create progress bar container
      const progressContainer = document.createElement('div');
      progressContainer.className = 'progress-bar';
      progressContainer.style.marginBottom = '20px';

      // Create progress bar fill
      progressBar = document.createElement('div');
      progressBar.className = 'progress-bar-fill';
      progressBar.id = 'rag-progress-bar';

      // Add to container
      progressContainer.appendChild(progressBar);

      // Insert after status
      const statusElement = document.getElementById('rag-status');
      statusElement.parentNode.insertBefore(progressContainer, statusElement.nextSibling);
    }

    // Update progress
    progressBar.style.width = `${percent}%`;

    // Update status text
    ragStatus.textContent = `Building RAG index... ${percent}%`;
  }
});

// Listen for Python process exit
ipcRenderer.on('python-exit', (event, code) => {
  addLog(`Python process exited with code ${code}`);
  if (code !== 0) {
    updateStatus('Error: Python process terminated');
  }
  // Update the Python process state
  pythonProcessRunning = false;
});

// Listen for Python process start
ipcRenderer.on('python-start', () => {
  // Update the Python process state
  pythonProcessRunning = true;
  addLog('Python process started');

  // Apply the user's preferred mode when the Python process starts
  // This ensures the mode is set correctly for the current session
  if (ragIndexBuilt) {
    // Small delay to ensure the Python process is ready
    setTimeout(() => {
      // Clear any Intelligent Mode error status if Intelligent Mode is enabled
      if (userSettings.intelligentModeEnabled) {
        clearIntelligentModeError();
      }

      applyUserPreferredMode()
        .then(() => {
          console.log('Applied user preferred mode after Python process start');
        })
        .catch(error => {
          console.error('Error applying user preferred mode after Python process start:', error);
        });
    }, 2000);
  }
});

// Listen for crawl progress updates
ipcRenderer.on('crawl-progress', (event, data) => {
  const percentage = Math.round((data.current / data.total) * 100);
  crawlProgress.style.width = `${percentage}%`;
  updateStatus(`Crawling: ${data.current}/${data.total} pages (${percentage}%)`);
});

// Listen for log messages
ipcRenderer.on('log', (event, message) => {
  addLog(message);
});

// Listen for crawl completion
ipcRenderer.on('crawl-complete', (event, complete) => {
  if (complete) {
    crawlComplete = true;
    analysisStatus.textContent = 'Crawl complete. Choose an analysis method:';
    updateStatus('Ready for analysis');
  }
});

// Helper function to safely remove processing indicators
function safelyRemoveProcessingIndicator(message) {
  // If message is null or undefined, try to find and remove all processing indicators
  if (!message) {
    try {
      console.log('No message provided, attempting to remove all processing indicators');
      const processingIndicators = document.querySelectorAll('.processing');
      processingIndicators.forEach(indicator => {
        try {
          indicator.remove();
        } catch (err) {
          console.error('Error removing processing indicator from DOM:', err);
        }
      });
    } catch (error) {
      console.error('Error in global processing indicator cleanup:', error);
    }
    return;
  }

  try {
    // First check if message is a valid object
    if (typeof message === 'object' && message !== null) {
      // Check if processingIndicator exists
      if (message.processingIndicator) {
        console.log('Found processingIndicator, attempting to remove');

        // Check if the processingIndicator is a DOM element with a remove method
        if (message.processingIndicator.remove &&
            typeof message.processingIndicator.remove === 'function') {
          message.processingIndicator.remove();
          console.log('Successfully removed processingIndicator using its remove method');
        }
        // If it's not a DOM element but we have a reference to it
        else {
          console.log('ProcessingIndicator does not have a remove method, searching DOM');
          // Try to find all processing indicators in the DOM and remove them
          const processingIndicators = document.querySelectorAll('.processing');
          processingIndicators.forEach(indicator => {
            try {
              indicator.remove();
              console.log('Removed processing indicator from DOM');
            } catch (err) {
              console.error('Error removing processing indicator from DOM:', err);
            }
          });
        }
      } else {
        console.log('No processingIndicator found on message, searching DOM');
        // If no processingIndicator property, try to find them in the DOM
        const processingIndicators = document.querySelectorAll('.processing');
        processingIndicators.forEach(indicator => {
          try {
            indicator.remove();
            console.log('Removed processing indicator from DOM');
          } catch (err) {
            console.error('Error removing processing indicator from DOM:', err);
          }
        });
      }
    } else {
      console.log('Message is not a valid object, searching DOM for indicators');
      // If message is not a valid object, try to find indicators in the DOM
      const processingIndicators = document.querySelectorAll('.processing');
      processingIndicators.forEach(indicator => {
        try {
          indicator.remove();
          console.log('Removed processing indicator from DOM');
        } catch (err) {
          console.error('Error removing processing indicator from DOM:', err);
        }
      });
    }
  } catch (error) {
    console.error('Error in safelyRemoveProcessingIndicator:', error);
  } finally {
    // Always ensure we set processingIndicator to null if message is valid
    // This prevents future access attempts that could cause errors
    if (message && typeof message === 'object') {
      try {
        message.processingIndicator = null;
        console.log('Set processingIndicator to null');
      } catch (err) {
        console.error('Error setting processingIndicator to null:', err);
      }
    }
  }
}

// Function to add a message to the chat
function addChatMessage(type, content) {
  // Create message elements
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${type}`;

  // Ensure the message is visible with important styling
  messageDiv.style.display = 'flex';
  messageDiv.style.opacity = '1';
  messageDiv.style.visibility = 'visible';
  messageDiv.style.position = 'relative';
  messageDiv.style.zIndex = '2';

  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';

  // Ensure the content is visible with important styling
  contentDiv.style.display = 'block';
  contentDiv.style.opacity = '1';
  contentDiv.style.visibility = 'visible';
  contentDiv.style.maxHeight = 'none';
  contentDiv.style.overflow = 'visible';
  contentDiv.style.position = 'relative';
  contentDiv.style.zIndex = '2';

  // Set the content if provided
  if (content) {
    contentDiv.innerHTML = content;
  }

  // Add the message to the chat
  messageDiv.appendChild(contentDiv);
  answerContainer.appendChild(messageDiv);

  // Scroll to the bottom with a smooth animation
  scrollChatToBottom();

  return contentDiv;
}

// Function to ensure proper scrolling in the chat area
function scrollChatToBottom() {
  // Use requestAnimationFrame to ensure the DOM has updated
  requestAnimationFrame(() => {
    // Get the chat messages container (parent of answerContainer)
    const chatMessagesContainer = document.querySelector('.chat-messages');

    // Ensure the answer container has proper z-index
    if (answerContainer) {
      answerContainer.style.position = 'relative';
      answerContainer.style.zIndex = '0';
    }

    // Scroll both containers to ensure visibility
    if (chatMessagesContainer) {
      chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
      chatMessagesContainer.style.position = 'relative';
      chatMessagesContainer.style.zIndex = '0';
    }

    answerContainer.scrollTop = answerContainer.scrollHeight;

    // Double-check scrolling after a short delay to ensure all content is rendered
    setTimeout(() => {
      if (chatMessagesContainer) {
        chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
      }
      answerContainer.scrollTop = answerContainer.scrollHeight;

      // Add a third check with a longer delay to ensure everything is properly rendered
      setTimeout(() => {
        if (chatMessagesContainer) {
          chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
        }
        answerContainer.scrollTop = answerContainer.scrollHeight;
      }, 500);
    }, 100);
  });
}

// Enhanced function to format the answer content with better visual styling
function formatAnswerContent(text) {
  // Clean up the text - remove any "A:" or "Answer:" prefixes
  let cleanText = text;
  if (cleanText.startsWith('A:')) {
    cleanText = cleanText.substring(2).trim();
  } else if (cleanText.startsWith('Answer:')) {
    cleanText = cleanText.substring(7).trim();
  } else if (cleanText.startsWith('Based on the document:')) {
    cleanText = cleanText.substring(22).trim();
    // Add a special document analysis badge
    cleanText = `<div class="analysis-badge">Document Analysis</div>${cleanText}`;
  }

  // Split the text into main answer and sources if present
  const parts = cleanText.split(/RELEVANT SOURCES|relevant sources|DOCUMENT SOURCE|document source/i);

  let mainContent = parts[0].trim();
  let sourcesContent = parts.length > 1 ? parts[1].trim() : '';

  // Format headings (before other formatting)
  mainContent = mainContent.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, content) => {
    const level = hashes.length;
    return `<h${level} class="content-heading">${content}</h${level}>`;
  });

  // Format blockquotes
  mainContent = mainContent.replace(/^>\s+(.+)$/gm, (match, content) => {
    return `<blockquote>${content}</blockquote>`;
  });

  // Convert URLs in the main content to clickable links
  mainContent = mainContent.replace(
    /(https?:\/\/[^\s\)]+)/g,
    '<a href="$1" target="_blank" class="content-link">$1</a>'
  );

  // Format bold text
  mainContent = mainContent.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
  mainContent = mainContent.replace(/__(.+?)__/g, '<strong>$1</strong>');

  // Format italic text
  mainContent = mainContent.replace(/\*(.+?)\*/g, '<em>$1</em>');
  mainContent = mainContent.replace(/_(.+?)_/g, '<em>$1</em>');

  // Format code blocks first (before other formatting)
  mainContent = formatCodeBlocks(mainContent);

  // Format lists
  mainContent = formatLists(mainContent);

  // Format tables if present
  mainContent = formatTables(mainContent);

  // Format the main content - paragraphs and line breaks
  // Only apply paragraph formatting to text not in code blocks or lists
  mainContent = mainContent
    .replace(/\n\n(?!<pre>|<ul>|<ol>|<table>|<blockquote>|<h[1-6])/g, '</p><p class="content-paragraph">')
    .replace(/\n(?!<pre>|<ul>|<ol>|<table>|<\/li>|<blockquote>|<h[1-6])/g, '<br>');

  // Add special styling for key points and important information
  mainContent = mainContent.replace(/(Key Points:|Important:|Note:|Warning:)(.+?)(?=<\/p>|<h[1-6]|$)/gs,
    (match, prefix, content) => {
      const className = prefix.toLowerCase().replace(':', '-');
      return `<div class="highlight ${className}">${prefix}${content}</div>`;
    }
  );

  // Format the sources if present
  let sourcesHtml = '';
  if (sourcesContent) {
    // Check if this is a document source
    if (sourcesContent.toLowerCase().includes('document:')) {
      // Format document source
      sourcesHtml = '<div class="sources-section document-source">' +
        '<h4>DOCUMENT SOURCE</h4>' +
        '<p class="document-name">' + sourcesContent.replace(/\n/g, '<br>') + '</p>' +
        '</div>';
    } else {
      // Try different regex patterns to extract URLs
      // First try the standard format with bullet points
      const urlRegex1 = /- <?(https?:\/\/[^\s>]+)>?/g;
      // Then try URLs that might appear without bullet points
      const urlRegex2 = /(https?:\/\/[^\s>]+)/g;
      // Also try numbered list format
      const urlRegex3 = /\d+\.\s+<?(https?:\/\/[^\s>]+)>?/g;

      const sources = [];
      let match;

      // Try all regex patterns
      [urlRegex1, urlRegex3, urlRegex2].forEach(regex => {
        if (sources.length === 0) {
          while ((match = regex.exec(sourcesContent)) !== null) {
            sources.push(match[1] || match[0]);
          }
        }
      });

      if (sources.length > 0) {
        // Remove duplicates
        const uniqueSources = [...new Set(sources)];

        sourcesHtml = '<div class="sources-section">' +
          '<h4>RELEVANT SOURCES</h4>' +
          '<ul class="source-list">' +
          uniqueSources.map(url => `<li><a href="${url}" target="_blank" class="source-link">${url}</a></li>`).join('') +
          '</ul>' +
          '</div>';
      } else {
        // If no URLs were found with the regex, just display the sources content as is
        sourcesHtml = '<div class="sources-section">' +
          '<h4>RELEVANT SOURCES</h4>' +
          '<p class="source-text">' + sourcesContent.replace(/\n/g, '<br>') + '</p>' +
          '</div>';
      }
    }
  }

  // Wrap the content in a div with a class for styling, ensuring no height limitations
  return `<div class="enhanced-content" style="max-height: none; overflow: visible;"><p class="content-paragraph">${mainContent}</p>${sourcesHtml}</div>`;
}

// Enhanced helper function to format code blocks with better styling
function formatCodeBlocks(text) {
  // Replace ```language\ncode\n``` style code blocks
  let formattedText = text.replace(/```([a-z]*)\n([\s\S]*?)\n```/g, function(match, language, code) {
    // Escape HTML in the code
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    // Add language label if specified
    const langLabel = language ?
      `<div class="code-language">${language}</div>` : '';

    return `<div class="code-block-wrapper">
      ${langLabel}
      <pre><code class="language-${language}">${escapedCode}</code></pre>
      <button class="copy-code-button" onclick="navigator.clipboard.writeText(\`${code.replace(/`/g, '\\`')}\`)">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
      </button>
    </div>`;
  });

  // Replace inline code with `code` backticks
  formattedText = formattedText.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

  return formattedText;
}

// Helper function to format lists
function formatLists(text) {
  let formattedText = text;

  // Format unordered lists (lines starting with -, *, or •)
  const ulRegex = /((?:^|\n)(?:- |\* |• ).+(?:\n(?:- |\* |• ).+)*)/g;
  formattedText = formattedText.replace(ulRegex, function(match) {
    const listItems = match.split('\n').filter(line => {
      const trimmed = line.trim();
      return trimmed.startsWith('- ') || trimmed.startsWith('* ') || trimmed.startsWith('• ');
    });
    const formattedItems = listItems.map(item => {
      // Remove the bullet point and trim
      let content = item.replace(/^-\s+|\*\s+|•\s+/, '').trim();
      // Convert any URLs in the list item to links
      content = content.replace(/(https?:\/\/[^\s\)]+)/g, '<a href="$1" target="_blank">$1</a>');
      return `<li>${content}</li>`;
    }).join('');
    return `<ul>${formattedItems}</ul>`;
  });

  // Format ordered lists (lines starting with 1., 2., etc.)
  const olRegex = /((?:^|\n)(?:\d+\. ).+(?:\n(?:\d+\. ).+)*)/g;
  formattedText = formattedText.replace(olRegex, function(match) {
    const listItems = match.split('\n').filter(line => /^\d+\.\s+/.test(line.trim()));
    const formattedItems = listItems.map(item => {
      // Remove the number and trim
      let content = item.replace(/^\d+\.\s+/, '').trim();
      // Convert any URLs in the list item to links
      content = content.replace(/(https?:\/\/[^\s\)]+)/g, '<a href="$1" target="_blank">$1</a>');
      return `<li>${content}</li>`;
    }).join('');
    return `<ol>${formattedItems}</ol>`;
  });

  return formattedText;
}

// Function to format Deep Research content with enhanced styling
function formatDeepResearchContent(text) {
  // Clean up the text - remove any "A:" or "Answer:" prefixes
  let cleanText = text;
  if (cleanText.startsWith('A:')) {
    cleanText = cleanText.substring(2).trim();
  } else if (cleanText.startsWith('Answer:')) {
    cleanText = cleanText.substring(7).trim();
  }

  // Add the Answer: prefix back to ensure proper display
  if (!cleanText.startsWith('Answer:') && !cleanText.startsWith('A:')) {
    cleanText = 'Answer: ' + cleanText;
  }

  // Remove any RELEVANT SOURCES section - we'll handle that separately
  const relevantSourcesIndex = cleanText.indexOf('RELEVANT SOURCES');
  if (relevantSourcesIndex > 0) {
    cleanText = cleanText.substring(0, relevantSourcesIndex).trim();
  }

  // Format headings (before other formatting)
  cleanText = cleanText.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, content) => {
    const level = hashes.length;
    return `<h${level} class="content-heading">${content}</h${level}>`;
  });

  // Format bold text with ** or __ (do this before lists to avoid conflicts)
  cleanText = cleanText.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
  cleanText = cleanText.replace(/__(.+?)__/g, '<strong>$1</strong>');

  // Format italic text with * or _ (do this before lists to avoid conflicts)
  cleanText = cleanText.replace(/\*([^\*]+?)\*/g, '<em>$1</em>');
  cleanText = cleanText.replace(/_([^_]+?)_/g, '<em>$1</em>');

  // Format unordered lists (lines starting with -, *, or •)
  const ulRegex = /((?:^|\n)(?:- |\* |• ).+(?:\n(?:- |\* |• ).+)*)/g;
  cleanText = cleanText.replace(ulRegex, function(match) {
    const listItems = match.split('\n').filter(line => {
      const trimmed = line.trim();
      return trimmed.startsWith('- ') || trimmed.startsWith('* ') || trimmed.startsWith('• ');
    });
    const formattedItems = listItems.map(item => {
      // Remove the bullet point and trim
      let content = item.replace(/^-\s+|\*\s+|•\s+/, '').trim();
      // Convert any URLs in the list item to links
      content = content.replace(/(https?:\/\/[^\s\)]+)/g, '<a href="$1" target="_blank">$1</a>');
      return `<li>${content}</li>`;
    }).join('');
    return `<ul class="deep-research-list">${formattedItems}</ul>`;
  });

  // Format ordered lists (lines starting with 1., 2., etc.)
  const olRegex = /((?:^|\n)(?:\d+\. ).+(?:\n(?:\d+\. ).+)*)/g;
  cleanText = cleanText.replace(olRegex, function(match) {
    const listItems = match.split('\n').filter(line => /^\d+\.\s+/.test(line.trim()));
    const formattedItems = listItems.map(item => {
      // Remove the number and trim
      let content = item.replace(/^\d+\.\s+/, '').trim();
      // Convert any URLs in the list item to links
      content = content.replace(/(https?:\/\/[^\s\)]+)/g, '<a href="$1" target="_blank">$1</a>');
      return `<li>${content}</li>`;
    }).join('');
    return `<ol class="deep-research-list">${formattedItems}</ol>`;
  });

  // Format blockquotes
  cleanText = cleanText.replace(/^>\s+(.+)$/gm, (match, content) => {
    return `<blockquote class="deep-research-quote">${content}</blockquote>`;
  });

  // Convert URLs to clickable links (that aren't already in links)
  cleanText = cleanText.replace(
    /(?<!href="|">)(https?:\/\/[^\s\)<]+)/g,
    '<a href="$1" target="_blank" class="content-link">$1</a>'
  );

  // Format code blocks
  cleanText = formatCodeBlocks(cleanText);

  // Format tables
  cleanText = formatTables(cleanText);

  // Format paragraphs and line breaks - improved to handle HTML tags better
  cleanText = cleanText
    .split('\n\n')
    .map(paragraph => {
      // Skip paragraphs that are already formatted as HTML
      if (paragraph.trim().startsWith('<') &&
          (paragraph.includes('</') || paragraph.includes('/>'))) {
        return paragraph;
      }
      // Skip empty paragraphs
      if (paragraph.trim() === '') {
        return '';
      }
      return `<p class="content-paragraph">${paragraph.replace(/\n/g, '<br>')}</p>`;
    })
    .join('');

  // Add special styling for sections
  cleanText = cleanText.replace(/<h([1-3])([^>]*)>([^<]+)<\/h\1>/g, (match, level, attrs, content) => {
    return `<div class="content-section"><h${level}${attrs}>${content}</h${level}></div>`;
  });

  // Add special styling for the "Additional Search Queries" section if present
  if (cleanText.includes('ADDITIONAL SEARCH QUERIES') || cleanText.includes('Additional Search Queries')) {
    cleanText = cleanText.replace(
      /(<h[1-3][^>]*>)(ADDITIONAL SEARCH QUERIES|Additional Search Queries)(<\/h[1-3]>)/g,
      '<div class="additional-queries-section">$1$2$3'
    );
    // Find the next heading to close the div
    const nextHeadingMatch = /<h[1-3][^>]*>/g.exec(cleanText.split('ADDITIONAL SEARCH QUERIES')[1]);
    if (nextHeadingMatch) {
      const index = cleanText.indexOf(nextHeadingMatch[0], cleanText.indexOf('ADDITIONAL SEARCH QUERIES'));
      if (index > 0) {
        cleanText = cleanText.substring(0, index) + '</div>' + cleanText.substring(index);
      } else {
        cleanText += '</div>';
      }
    } else {
      cleanText += '</div>';
    }
  }

  // Return the formatted content without wrapping it in a div
  // (The wrapping will be done later in the code)
  return cleanText;
}

// Helper function to format tables
function formatTables(text) {
  // Look for markdown tables
  // A table has a header row, a separator row, and at least one data row
  const tableRegex = /\|(.+)\|\n\|([-:| ]+)\|\n(\|.+\|\n?)+/g;

  return text.replace(tableRegex, function(match) {
    const rows = match.trim().split('\n');

    // Process header row
    const headerRow = rows[0];
    const headerCells = headerRow.split('|').slice(1, -1);
    const headerHtml = headerCells.map(cell => `<th>${cell.trim()}</th>`).join('');

    // Skip separator row

    // Process data rows
    const dataRows = rows.slice(2);
    const dataRowsHtml = dataRows.map(row => {
      const cells = row.split('|').slice(1, -1);
      return `<tr>${cells.map(cell => `<td>${cell.trim()}</td>`).join('')}</tr>`;
    }).join('');

    return `<table><thead><tr>${headerHtml}</tr></thead><tbody>${dataRowsHtml}</tbody></table>`;
  });
}

// Variable to track the current assistant message being built
let currentAssistantMessage = null;
let messageBuffer = '';

// Listen for partial RAG answers (streaming)
ipcRenderer.on('rag-partial-answer', (event, partialAnswer) => {
  console.log('Received partial answer:', partialAnswer);

  // If we don't have a current message, something went wrong
  // The message should have been created by the query button click handler
  if (!currentAssistantMessage) {
    console.error('No current assistant message found for partial answer');
    return;
  }

  // Add this partial answer to the buffer
  messageBuffer += partialAnswer + '\n';

  // Update the message content (without the processing indicator)
  currentAssistantMessage.innerHTML = formatAnswerContent(messageBuffer);

  // Scroll to the bottom
  answerContainer.scrollTop = answerContainer.scrollHeight;

  // Update status
  ragStatus.textContent = 'Receiving answer...';
  updateStatus('Receiving answer...');
});

// Listen for complete RAG answers
ipcRenderer.on('rag-answer', (event, answer) => {
  console.log('Received complete RAG answer');

  // If there's a processing indicator, remove it
  safelyRemoveProcessingIndicator(currentAssistantMessage);

  // Format the answer content
  const formattedAnswer = formatAnswerContent(answer);

  // Update the message with the complete answer
  if (currentAssistantMessage) {
    currentAssistantMessage.innerHTML = formattedAnswer;
  } else {
    // If for some reason we don't have a current message, create one
    console.warn('No current assistant message found for complete answer, creating new one');

    // Get the last query from the input field or use a placeholder
    const lastQuery = queryInput.value.trim() || 'your question';

    // Add the user message
    addChatMessage('user', `<p>${lastQuery}</p>`);

    // Add the assistant message with the answer
    const assistantMessage = addChatMessage('assistant', '');
    assistantMessage.innerHTML = formattedAnswer;
  }

  // The backend already stores the assistant's response in the conversation manager
  // No need to store it again here to prevent duplication

  // Reset the current message
  currentAssistantMessage = null;
  messageBuffer = '';

  // Scroll to the bottom
  answerContainer.scrollTop = answerContainer.scrollHeight;

  // Update status
  ragStatus.textContent = 'Query processed successfully.';
  updateStatus('Answer received');
});

// Listen for query completion signal
ipcRenderer.on('rag-query-complete', () => {
  console.log('Query complete, re-enabling input');

  // Re-enable the query input and button
  queryInput.disabled = false;
  queryButton.disabled = false;

  // Focus on the input field
  queryInput.focus();
});

// Listen for tools query completion
ipcRenderer.on('tools-query-complete', (event, result) => {
  console.log('Tools query complete');

  // If there's a processing indicator, remove it
  const processingIndicator = document.querySelector('.message.assistant .processing-indicator');
  if (processingIndicator) {
    const messageElement = processingIndicator.closest('.message');
    if (messageElement) {
      messageElement.remove();
    }
  }

  // Format and display the result
  const formattedResult = result || "No relevant tools or procedures found.";
  addChatMessage('assistant', formatToolsResponse(formattedResult));

  // Re-enable the query input and button
  queryInput.disabled = false;
  queryButton.disabled = false;

  // Update status
  ragStatus.textContent = 'Tools query complete';
  updateStatus('Tools query complete');

  // Focus on the input field
  queryInput.focus();
});

// Listen for intelligent query completion
ipcRenderer.on('intelligent-query-complete', (event, result) => {
  console.log('Intelligent query complete:', result);

  // Clear any Intelligent Mode error status on successful completion
  clearIntelligentModeError();

  // If we already have a currentAssistantMessage, update it instead of creating a new one
  if (currentAssistantMessage) {
    console.log('Updating existing assistant message with intelligent query result');

    // Remove the processing indicator if it exists
    safelyRemoveProcessingIndicator(currentAssistantMessage);

    // Format and update the existing message
    const formattedResult = formatIntelligentResponse(result);
    currentAssistantMessage.innerHTML = formattedResult;
  } else {
    console.log('No current assistant message found, creating a new one');

    // If there's a processing indicator in any message, remove it
    const processingIndicator = document.querySelector('.message.assistant .processing-indicator');
    if (processingIndicator) {
      const messageElement = processingIndicator.closest('.message');
      if (messageElement) {
        messageElement.remove();
      }
    }

    // Format and display the result in a new message
    const formattedResult = formatIntelligentResponse(result);
    addChatMessage('assistant', formattedResult);
  }

  // Reset the current message reference
  currentAssistantMessage = null;

  // Re-enable the query input and button
  queryInput.disabled = false;
  queryButton.disabled = false;

  // Update status
  ragStatus.textContent = 'Intelligent Mode processing complete';
  updateStatus('Processing complete');

  // Focus on the input field
  queryInput.focus();
});

// Listen for intelligent query errors
ipcRenderer.on('intelligent-query-error', (event, data) => {
  console.error('Intelligent query error:', data.error);

  // If there's a processing indicator, remove it
  const processingIndicator = document.querySelector('.message.assistant .processing-indicator');
  if (processingIndicator) {
    const messageElement = processingIndicator.closest('.message');
    if (messageElement) {
      messageElement.remove();
    }
  }

  // Add error message to chat
  addChatMessage('system', `<p>Error: ${data.error}</p>`);

  // Re-enable the query input and button
  queryInput.disabled = false;
  queryButton.disabled = false;

  // Update status
  ragStatus.textContent = 'Error in Intelligent Mode';
  updateStatus('Processing complete');

  // Focus on the input field
  queryInput.focus();
});

// Listen for tool confirmation requests
ipcRenderer.on('show-tool-confirmation', (event, data) => {
  console.log('Showing tool confirmation dialog for:', data.tool);

  // Create the modal container if it doesn't exist
  let modalContainer = document.getElementById('tool-confirmation-modal');
  if (!modalContainer) {
    modalContainer = document.createElement('div');
    modalContainer.id = 'tool-confirmation-modal';
    modalContainer.className = 'modal-container';
    document.body.appendChild(modalContainer);

    // Add styles for the modal
    const style = document.createElement('style');
    style.textContent = `
      .modal-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }
      .modal-container.visible {
        opacity: 1;
        visibility: visible;
      }
      .modal-content {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        transform: translateY(-20px);
        transition: transform 0.3s ease;
      }
      .modal-container.visible .modal-content {
        transform: translateY(0);
      }
      .modal-header {
        margin-top: 0;
        color: #2c3e50;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        margin-bottom: 15px;
      }
      .modal-body {
        margin-bottom: 20px;
        line-height: 1.5;
      }
      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        border-top: 1px solid #eee;
        padding-top: 15px;
      }
      .modal-button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s ease;
      }
      .modal-button.confirm {
        background-color: #4CAF50;
        color: white;
      }
      .modal-button.confirm:hover {
        background-color: #3d9140;
      }
      .modal-button.cancel {
        background-color: #f44336;
        color: white;
      }
      .modal-button.cancel:hover {
        background-color: #d32f2f;
      }
    `;
    document.head.appendChild(style);
  }

  // Create the modal content
  modalContainer.innerHTML = `
    <div class="modal-content">
      <h2 class="modal-header">Confirm Action</h2>
      <div class="modal-body">
        <p>The system detected that you want to use <strong>${data.tool}</strong> for your query.</p>
        <p><strong>Reason:</strong> ${data.reason}</p>
        <p>Do you want to proceed with this action?</p>
      </div>
      <div class="modal-footer">
        <button class="modal-button cancel" id="cancel-tool">Cancel</button>
        <button class="modal-button confirm" id="confirm-tool">Confirm</button>
      </div>
    </div>
  `;

  // Show the modal with animation
  setTimeout(() => {
    modalContainer.classList.add('visible');
  }, 10);

  // Add event listeners for the buttons
  document.getElementById('confirm-tool').addEventListener('click', () => {
    // Hide the modal
    modalContainer.classList.remove('visible');

    // Wait for the animation to complete before removing
    setTimeout(() => {
      modalContainer.style.display = 'none';

      // Send the confirmation back to the main process
      ipcRenderer.send('tool-confirmation-response', true);
    }, 300);
  });

  document.getElementById('cancel-tool').addEventListener('click', () => {
    // Hide the modal
    modalContainer.classList.remove('visible');

    // Wait for the animation to complete before removing
    setTimeout(() => {
      modalContainer.style.display = 'none';

      // Send the rejection back to the main process
      ipcRenderer.send('tool-confirmation-response', false);
    }, 300);
  });

  // Also close when clicking outside the modal content
  modalContainer.addEventListener('click', (e) => {
    if (e.target === modalContainer) {
      modalContainer.classList.remove('visible');

      // Wait for the animation to complete before removing
      setTimeout(() => {
        modalContainer.style.display = 'none';

        // Send the rejection back to the main process
        ipcRenderer.send('tool-confirmation-response', false);
      }, 300);
    }
  });
});

// Listen for Deep Research completion
ipcRenderer.on('deep-research-complete', (event, data) => {
  console.log('Deep Research complete:', data.query);
  console.log(`Sources count: ${data.sources.length}`);

  // Log detailed debugging information
  console.log('='.repeat(40));
  console.log('DEEP RESEARCH RESPONSE DEBUG');
  console.log('='.repeat(40));
  console.log(`Analysis text length: ${data.analysis ? data.analysis.length : 0} characters`);
  if (data.analysis) {
    console.log('Analysis text first 200 chars: ' + data.analysis.substring(0, 200));
    console.log('Analysis text last 200 chars: ' + data.analysis.substring(data.analysis.length - 200));
  } else {
    console.error('ERROR: Analysis text is undefined or null');
  }
  console.log('='.repeat(40));

  // Remove the processing indicator if it exists
  safelyRemoveProcessingIndicator(currentAssistantMessage);

  // Format the sources for display with improved styling
  let sourcesHtml = '<div class="sources-section"><h4>RELEVANT SOURCES</h4><ul class="source-list">';

  // Remove duplicate sources by URL
  const uniqueSources = [];
  const seenUrls = new Set();

  data.sources.forEach(source => {
    // Skip empty or invalid URLs
    if (!source.url || source.url === 'undefined' || source.url === 'null') {
      return;
    }

    // Skip duplicate URLs
    if (seenUrls.has(source.url)) {
      return;
    }

    seenUrls.add(source.url);
    uniqueSources.push(source);
  });

  // Log the number of unique sources
  console.log(`Found ${uniqueSources.length} unique sources out of ${data.sources.length} total sources`);

  // Add each unique source to the HTML
  uniqueSources.forEach((source, index) => {
    // Format the title - use the title if available, otherwise use the URL
    const displayTitle = source.title || source.url.split('/').pop() || `Source ${index + 1}`;

    // Add the source to the HTML with a number prefix
    sourcesHtml += `<li><a href="${source.url}" target="_blank" class="source-link">${index + 1}. ${displayTitle}</a></li>`;
  });

  sourcesHtml += '</ul></div>';
  console.log(`Generated sources HTML (${sourcesHtml.length} characters)`);

  // Process the analysis text to ensure it's properly formatted
  let analysisText = data.analysis;

  // Check if analysis text is valid
  if (!analysisText) {
    console.error('ERROR: Analysis text is undefined or null');
    analysisText = 'Answer: An error occurred while processing the research results. Please try again.';
  }

  // Log the length of the analysis for debugging
  console.log(`Deep Research analysis length: ${analysisText.length} characters`);

  // Clean up the text - remove any "A:" or "Answer:" prefixes if they appear twice
  if (analysisText.startsWith('Answer: Answer:')) {
    console.log('Fixing double "Answer:" prefix');
    analysisText = analysisText.replace('Answer: Answer:', 'Answer:');
  } else if (analysisText.startsWith('A: A:')) {
    console.log('Fixing double "A:" prefix');
    analysisText = analysisText.replace('A: A:', 'A:');
  } else if (analysisText.includes('================================================================================')){
    console.log('Fixing duplicate response with separator');
    // Split by the separator and take only the first part
    analysisText = analysisText.split('================================================================================')[0].trim();
  }

  // Ensure we only have one "Answer:" prefix
  const answerPrefixRegex = /^(Answer:|A:)\s*/i;
  if (answerPrefixRegex.test(analysisText)) {
    console.log('Ensuring single Answer prefix');
    // Remove the prefix for now, we'll add it back in a consistent format
    analysisText = analysisText.replace(answerPrefixRegex, '');
    // Add back a consistent prefix
    analysisText = 'Answer: ' + analysisText;
  } else if (!analysisText.startsWith('Answer:') && !analysisText.startsWith('A:')) {
    // If there's no prefix at all, add one to ensure proper display
    console.log('Adding missing Answer prefix');
    analysisText = 'Answer: ' + analysisText;
  }

  // Make sure the analysis has proper formatting
  let formattedAnalysis = analysisText;

  console.log('Checking if analysis needs formatting...');

  // If the analysis doesn't already contain HTML formatting, apply our formatting
  if (!formattedAnalysis.includes('<h') && !formattedAnalysis.includes('<p>')) {
    console.log('Analysis does not contain HTML formatting, applying custom formatting');

    // Split the text into main answer and sources if present
    const parts = formattedAnalysis.split(/RELEVANT SOURCES|relevant sources|DOCUMENT SOURCE|document source/i);
    console.log(`Split analysis into ${parts.length} parts`);
    let mainContent = parts[0].trim();
    console.log(`Main content length: ${mainContent.length} characters`);

    // Format the main content with our enhanced formatter
    console.log('Applying formatDeepResearchContent to main content');
    formattedAnalysis = formatDeepResearchContent(mainContent);
    console.log(`Formatted analysis length: ${formattedAnalysis.length} characters`);
  } else {
    console.log('Analysis already contains HTML formatting, skipping custom formatting');
  }

  // Always use the "Read more" functionality for Deep Research responses
  // This ensures that long responses are properly displayed
  console.log(`Deep Research response length: ${analysisText.length} characters. Applying special formatting.`);

  // For all responses, add a "Read more" section that expands when clicked
  // This ensures that the content is properly displayed
  const previewLength = 50000; // Increased from 2000
  console.log(`Creating preview with first ${previewLength} characters`);

  // Make sure we don't cut off in the middle of an HTML tag
  let previewContent = formattedAnalysis;

  // If the content is longer than the preview length, create a preview
  if (formattedAnalysis.length > previewLength) {
    // Find a safe place to cut the preview (at a closing tag)
    let cutIndex = previewLength;
    while (cutIndex < formattedAnalysis.length && formattedAnalysis[cutIndex] !== '>' && cutIndex < previewLength + 100) {
      cutIndex++;
    }
    if (cutIndex < formattedAnalysis.length) {
      cutIndex++; // Include the closing bracket
    }

    previewContent = formattedAnalysis.substring(0, cutIndex);
    console.log(`Preview content length: ${previewContent.length} characters`);

    formattedAnalysis = `
      <div class="deep-research-content" style="max-height: none; overflow: visible;">
        <div class="content-preview" style="max-height: none; overflow: visible;">${previewContent}...</div>
        <div class="content-full" style="display: none; max-height: none; overflow: visible;">${formattedAnalysis}</div>
        <button class="read-more-button">Read full analysis</button>
      </div>
    `;
  } else {
    // If the content is short, just wrap it in the deep-research-content div
    formattedAnalysis = `<div class="deep-research-content" style="max-height: none; overflow: visible;">${formattedAnalysis}</div>`;
  }

  console.log(`Final formatted content length: ${formattedAnalysis.length} characters`);

  // Add event listener for the read more button after a short delay
  setTimeout(() => {
    console.log('Setting up read more button event listener');
    const readMoreButton = document.querySelector('.read-more-button');
    if (readMoreButton) {
      readMoreButton.addEventListener('click', function() {
        console.log('Read more button clicked');
        const preview = this.parentNode.querySelector('.content-preview');
        const full = this.parentNode.querySelector('.content-full');

        if (preview.style.display !== 'none') {
          console.log('Showing full content');
          preview.style.display = 'none';
          full.style.display = 'block';
          this.textContent = 'Show less';

          // Scroll to make sure the button is visible
          this.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
          console.log('Showing preview content');
          preview.style.display = 'block';
          full.style.display = 'none';
          this.textContent = 'Read full analysis';

          // Scroll to the top of the message
          this.parentNode.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
      console.log('Read more button event listener set up successfully');
    } else {
      console.error('Read more button not found in the DOM');
    }
  }, 500);

  // Update the message with the formatted analysis and sources
  console.log('Preparing to update message with formatted content');

  // Combine the formatted analysis and sources
  const finalContent = `${formattedAnalysis}${sourcesHtml}`;
  console.log(`Final content length: ${finalContent.length} characters`);

  // Create a wrapper div to ensure proper styling and visibility
  const wrappedContent = `<div class="deep-research-wrapper" style="display: block; visibility: visible; opacity: 1;">${finalContent}</div>`;

  if (currentAssistantMessage) {
    console.log('Updating existing assistant message with formatted content');
    currentAssistantMessage.innerHTML = wrappedContent;
    console.log('Assistant message updated successfully');

    // Ensure the message is visible with important styling
    currentAssistantMessage.style.display = 'block';
    currentAssistantMessage.style.opacity = '1';
    currentAssistantMessage.style.visibility = 'visible';
  } else {
    // If for some reason we don't have a current message, create one
    console.warn('No current assistant message found for Deep Research result, creating new one');
    const assistantMessage = addChatMessage('assistant', '');
    assistantMessage.innerHTML = wrappedContent;
    console.log('Created new assistant message with formatted content');

    // Ensure the message is visible with important styling
    assistantMessage.style.display = 'block';
    assistantMessage.style.opacity = '1';
    assistantMessage.style.visibility = 'visible';
  }

  // Scroll to the bottom
  console.log('Scrolling chat to bottom');
  scrollChatToBottom();

  // Update status
  console.log('Updating status indicators');
  ragStatus.textContent = 'Deep Research complete';
  updateStatus('Deep Research complete');

  // Explicitly re-enable the input field and button
  console.log('Re-enabling input field and button');
  queryInput.disabled = false;
  queryButton.disabled = false;

  // Focus on the input field
  console.log('Setting focus to input field');
  queryInput.focus();

  console.log('Deep Research response handling complete');
  console.log('='.repeat(40));
});

// Listen for Deep Research errors
ipcRenderer.on('deep-research-error', (event, data) => {
  console.error('Deep Research error:', data.error);

  // Remove the processing indicator if it exists
  safelyRemoveProcessingIndicator(currentAssistantMessage);

  // Update the message with the error
  if (currentAssistantMessage) {
    currentAssistantMessage.innerHTML = `<p>Error: ${data.error}</p>`;
  } else {
    // If for some reason we don't have a current message, create one
    console.warn('No current assistant message found for Deep Research error, creating new one');
    const assistantMessage = addChatMessage('assistant', '');
    assistantMessage.innerHTML = `<p>Error: ${data.error}</p>`;
  }

  // Scroll to the bottom
  answerContainer.scrollTop = answerContainer.scrollHeight;

  // Update status
  ragStatus.textContent = 'Deep Research error';
  updateStatus('Error');

  // Explicitly re-enable the input field and button
  queryInput.disabled = false;
  queryButton.disabled = false;

  // Focus on the input field
  queryInput.focus();
});

// Listen for database status updates
ipcRenderer.on('database-status', (event, status) => {
  console.log('Database status:', status);

  // Update the status text
  updateStatus('Database ready');

  // Add to log
  addLog(status);

  // If we're on the setup tab, update the status there too
  if (document.getElementById('setup-tab').classList.contains('active')) {
    // Check if we have a database status element, if not create one
    let dbStatusElement = document.getElementById('db-status');
    if (!dbStatusElement) {
      dbStatusElement = document.createElement('div');
      dbStatusElement.id = 'db-status';
      dbStatusElement.className = 'database-status';
      dbStatusElement.style.marginTop = '20px';
      dbStatusElement.style.padding = '10px';
      dbStatusElement.style.borderRadius = '5px';
      dbStatusElement.style.backgroundColor = 'rgba(0, 229, 255, 0.1)';
      dbStatusElement.style.border = '1px solid var(--accent-color)';

      // Add it to the setup tab
      const setupCard = document.querySelector('#setup-tab .card');
      if (setupCard) {
        setupCard.appendChild(dbStatusElement);
      }
    }

    // Update the status text
    dbStatusElement.textContent = status;
  }
});

// Listen for existing data notification
ipcRenderer.on('existing-data', (event, hasData) => {
  console.log('Existing data check:', hasData);

  // If we have existing data, show a notification and enable analysis buttons
  if (hasData) {
    // Update status
    updateStatus('Database has existing data');

    // Create or update the existing data notification
    let existingDataElement = document.getElementById('existing-data-notice');
    if (!existingDataElement) {
      existingDataElement = document.createElement('div');
      existingDataElement.id = 'existing-data-notice';
      existingDataElement.className = 'existing-data-notice';
      existingDataElement.style.marginTop = '20px';
      existingDataElement.style.padding = '15px';
      existingDataElement.style.borderRadius = '5px';
      existingDataElement.style.backgroundColor = 'rgba(0, 255, 163, 0.1)';
      existingDataElement.style.border = '1px solid var(--success-color)';
      existingDataElement.style.color = 'var(--success-color)';

      // Add buttons for direct analysis and RAG
      const analysisButtons = document.createElement('div');
      analysisButtons.className = 'button-group';
      analysisButtons.style.marginTop = '15px';

      // Direct analysis button has been removed

      // RAG button
      const ragButton = document.createElement('button');
      ragButton.className = 'button primary';
      ragButton.textContent = 'RAG Q&A';
      ragButton.onclick = async () => {
        // Show loading state
        updateStatus('Starting RAG analysis of existing data...');
        addLog('Starting RAG analysis of existing data...');

        // Disable the button while processing
        ragButton.disabled = true;
        ragButton.textContent = 'Initializing...';

        try {
          // Simulate crawl completion to enable RAG
          crawlComplete = true;

          // Invoke the analyze-existing function with RAG method
          await ipcRenderer.invoke('analyze-existing', {
            method: 'rag'
          });

          // Wait a bit for the process to initialize
          await new Promise(resolve => setTimeout(resolve, 3000));

          // Set the RAG index as built
          ragIndexBuilt = true;

          // Switch to RAG tab
          switchToTab('rag');

          // Update RAG status
          ragStatus.textContent = 'Database loaded. You can now ask questions about the content.';
          updateStatus('RAG ready');
          queryInput.focus();

          // Re-enable the button
          ragButton.disabled = false;
          ragButton.textContent = 'RAG Q&A';
        } catch (error) {
          addLog(`Error: ${error.message}`);

          // Re-enable the button
          ragButton.disabled = false;
          ragButton.textContent = 'RAG Q&A';
        }
      };

      // Add button to container
      analysisButtons.appendChild(ragButton);

      // Add content and buttons to the notice
      existingDataElement.innerHTML = '<strong>Existing Data Found!</strong> You can analyze the previously crawled data without crawling again.';
      existingDataElement.appendChild(analysisButtons);

      // Add it to the setup tab
      const setupCard = document.querySelector('#setup-tab .card');
      if (setupCard) {
        setupCard.appendChild(existingDataElement);
      }
    }
  }
});

// Listen for automatic RAG selection
ipcRenderer.on('rag-selected', () => {
  // Switch to RAG tab if auto-switch is enabled
  if (userSettings.autoSwitchToRag) {
    setTimeout(() => {
      switchToTab('rag');
      updateStatus('Building RAG index...');
      ragStatus.textContent = 'Building RAG index...';
    }, 1000);
  }
});

// Chat with existing data button
chatButton.addEventListener('click', async () => {
  try {
    // Update status
    updateStatus('Starting chat with existing data...');
    addLog('Starting chat with existing data...');

    // Switch to RAG tab
    switchToTab('rag');

    // Clear previous results except welcome message
    const welcomeMessage = answerContainer.querySelector('.message.system');
    answerContainer.innerHTML = '';
    if (welcomeMessage) {
      answerContainer.appendChild(welcomeMessage);
    }

    ragStatus.textContent = 'Initializing chat with existing data...';

    // Start chat with existing data
    await ipcRenderer.invoke('start-chat');

    // Set the RAG index as built
    ragIndexBuilt = true;
    crawlComplete = true;

    // Load sessions
    loadSessions();

    // Focus on the query input
    queryInput.focus();

  } catch (error) {
    addLog(`Error starting chat: ${error.message}`);
    ragStatus.textContent = `Error: ${error.message}`;
  }
});

// Session Management Elements
const sessionsListElement = document.getElementById('sessions-list');
const newSessionButton = document.getElementById('new-session-button');
const sessionModal = document.getElementById('session-modal');
const sessionNameInput = document.getElementById('session-name-input');
const modalCloseButton = document.getElementById('modal-close');
const modalCancelButton = document.getElementById('modal-cancel');
const modalCreateButton = document.getElementById('modal-create');

// Session Management Functions
async function loadSessions() {
  try {
    // Get sessions from the backend
    sessions = await ipcRenderer.invoke('get-sessions');

    // Update the UI
    updateSessionsUI();

    // If there are sessions but no current session ID, select the first one
    if (sessions.length > 0 && !currentSessionId) {
      // Find the active session
      const activeSession = sessions.find(session => session.is_active);
      if (activeSession) {
        // Select the active session
        currentSessionId = activeSession.id;
        console.log(`Auto-selected active session: ${activeSession.name} (${activeSession.id})`);
      } else {
        // Select the first session
        currentSessionId = sessions[0].id;
        console.log(`Auto-selected first session: ${sessions[0].name} (${sessions[0].id})`);
        // Set it as active
        await ipcRenderer.invoke('set-active-session', currentSessionId);
      }

      // Load the session messages
      const messages = await ipcRenderer.invoke('get-session-messages', currentSessionId);

      // Add the messages to the chat container if it's empty
      if (answerContainer.children.length === 0) {
        messages.forEach(message => {
          addMessageToUI(message.sender, message.content);
        });

        // If there are no messages, add a welcome message
        if (messages.length === 0) {
          const welcomeMessage = document.createElement('div');
          welcomeMessage.className = 'message system';
          welcomeMessage.innerHTML = `
            <div class="message-content">
              <p>Welcome to a new chat session! Ask me anything about the website content.</p>
            </div>
          `;
          answerContainer.appendChild(welcomeMessage);
        }

        // Scroll to the bottom
        scrollChatToBottom();
      }
    }
  } catch (error) {
    console.error('Error loading sessions:', error);
  }
}

function updateSessionsUI() {
  // Clear the sessions list
  sessionsListElement.innerHTML = '';

  if (sessions.length === 0) {
    // Show empty state - just a message, no button (use the plus button instead)
    sessionsListElement.innerHTML = `
      <div class="empty-sessions">
        <p>No chat sessions yet</p>
        <p class="empty-sessions-hint">Click the + button above to create a new session</p>
      </div>
    `;
  } else {
    // Sort sessions by updated_at (most recent first)
    sessions.sort((a, b) => b.updated_at - a.updated_at);

    // Add each session to the list
    sessions.forEach(session => {
      const sessionElement = document.createElement('div');
      sessionElement.className = `session-item ${session.is_active ? 'active' : ''}`;
      sessionElement.dataset.id = session.id;

      // Format the date
      const date = new Date(session.updated_at);
      const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

      sessionElement.innerHTML = `
        <div class="session-info">
          <div class="session-name">${session.name}</div>
          <div class="session-date">${formattedDate}</div>
        </div>
        <div class="session-actions">
          <button class="session-action-button delete" title="Delete session">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3 6 5 6 21 6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            </svg>
          </button>
        </div>
      `;

      // Add event listener to select the session
      sessionElement.addEventListener('click', (e) => {
        // Ignore clicks on the delete button
        if (e.target.closest('.session-action-button.delete')) {
          return;
        }

        selectSession(session.id);
      });

      // Add event listener to delete the session
      const deleteButton = sessionElement.querySelector('.session-action-button.delete');
      deleteButton.addEventListener('click', (e) => {
        e.stopPropagation();
        deleteSession(session.id);
      });

      sessionsListElement.appendChild(sessionElement);
    });

    // If no active session, select the first one
    if (!currentSessionId && sessions.length > 0) {
      selectSession(sessions[0].id);
    }
  }
}

async function selectSession(sessionId) {
  try {
    // Set the session as active
    await ipcRenderer.invoke('set-active-session', sessionId);

    // Update the current session ID
    currentSessionId = sessionId;

    // Update the UI
    updateSessionsUI();

    // Load the session messages
    const messages = await ipcRenderer.invoke('get-session-messages', sessionId);

    // Clear the chat container
    answerContainer.innerHTML = '';

    // Add the messages to the chat container
    messages.forEach(message => {
      addMessageToUI(message.sender, message.content);
    });

    // If there are no messages, add a welcome message
    if (messages.length === 0) {
      const welcomeMessage = document.createElement('div');
      welcomeMessage.className = 'message system';
      welcomeMessage.innerHTML = `
        <div class="message-content">
          <p>Welcome to a new chat session! Ask me anything about the website content.</p>
        </div>
      `;
      answerContainer.appendChild(welcomeMessage);
    }

    // Scroll to the bottom
    scrollChatToBottom();
  } catch (error) {
    console.error('Error selecting session:', error);
  }
}

function addMessageToUI(sender, content) {
  const messageClass = sender === 'user' ? 'user' : (sender === 'assistant' ? 'assistant' : 'system');
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${messageClass}`;
  messageDiv.style.zIndex = '2';
  messageDiv.style.position = 'relative';

  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';
  contentDiv.innerHTML = content;
  contentDiv.style.zIndex = '2';
  contentDiv.style.position = 'relative';

  messageDiv.appendChild(contentDiv);
  answerContainer.appendChild(messageDiv);

  return contentDiv;
}

async function createSession(name) {
  try {
    // Create a new session
    const session = await ipcRenderer.invoke('create-session', name);

    // Update the current session ID
    currentSessionId = session.id;

    // Reload sessions
    await loadSessions();

    // Clear the chat container
    answerContainer.innerHTML = '';

    // Add a welcome message
    const welcomeMessage = document.createElement('div');
    welcomeMessage.className = 'message system';
    welcomeMessage.innerHTML = `
      <div class="message-content">
        <p>Welcome to a new chat session! Ask me anything about the website content.</p>
      </div>
    `;
    answerContainer.appendChild(welcomeMessage);

    // Scroll to the bottom
    scrollChatToBottom();
  } catch (error) {
    console.error('Error creating session:', error);
  }
}

async function deleteSession(sessionId) {
  try {
    // Confirm deletion
    if (!confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
      return;
    }

    // Delete the session
    await ipcRenderer.invoke('delete-session', sessionId);

    // If the deleted session was the current one, clear the current session ID
    if (sessionId === currentSessionId) {
      currentSessionId = null;
    }

    // Reload sessions
    await loadSessions();

    // If there are no sessions left, show the empty state
    if (sessions.length === 0) {
      // Clear the chat container
      answerContainer.innerHTML = '';

      // Add a welcome message
      const welcomeMessage = document.createElement('div');
      welcomeMessage.className = 'message system';
      welcomeMessage.innerHTML = `
        <div class="message-content">
          <p>Welcome to Fungi Knowledge Assistant! Click the + button in the sidebar to create a new session.</p>
        </div>
      `;
      answerContainer.appendChild(welcomeMessage);

      // Create a default session automatically
      setTimeout(() => {
        createSession('Default Session');
      }, 500);
    } else if (currentSessionId === null && sessions.length > 0) {
      // If there's no current session but there are sessions available, select the first one
      selectSession(sessions[0].id);
    }
  } catch (error) {
    console.error('Error deleting session:', error);
  }
}

function showCreateSessionModal() {
  // Show the modal
  sessionModal.classList.add('active');

  // Focus on the input
  sessionNameInput.value = '';
  sessionNameInput.focus();
}

function hideCreateSessionModal() {
  // Hide the modal
  sessionModal.classList.remove('active');
}

// Session Management Event Listeners
newSessionButton.addEventListener('click', showCreateSessionModal);

modalCloseButton.addEventListener('click', hideCreateSessionModal);
modalCancelButton.addEventListener('click', hideCreateSessionModal);

modalCreateButton.addEventListener('click', () => {
  const sessionName = sessionNameInput.value.trim();
  if (sessionName) {
    createSession(sessionName);
    hideCreateSessionModal();
  } else {
    alert('Please enter a session name');
    sessionNameInput.focus();
  }
});

// Allow pressing Enter to create a session
sessionNameInput.addEventListener('keydown', (e) => {
  if (e.key === 'Enter') {
    const sessionName = sessionNameInput.value.trim();
    if (sessionName) {
      createSession(sessionName);
      hideCreateSessionModal();
    } else {
      alert('Please enter a session name');
    }
  }
});

// Listen for RAG index building notification
ipcRenderer.on('rag-index-building', (event, isBuilding) => {
  if (isBuilding) {
    // Update the RAG status
    ragStatus.textContent = 'Building RAG index from existing data...';
    updateStatus('Building RAG index...');

    // Set the flag to indicate that the RAG index is being built
    ragIndexBuilt = false;

    // Switch to the RAG tab
    switchToTab('rag');
  }
});

// Settings elements
const defaultRagOnlyRadio = document.getElementById('default-rag-only');
const defaultRagRadio = document.getElementById('default-rag');
const defaultModelSelect = document.getElementById('default-model');
const autoSwitchRagCheckbox = document.getElementById('auto-switch-rag');
const intelligentModeCheckbox = document.getElementById('intelligent-mode-checkbox');
const autoSaveSummariesCheckbox = document.getElementById('auto-save-summaries-checkbox');
const intelligentBadge = document.getElementById('intelligent-badge');
const openrouterApiKeyInput = document.getElementById('openrouter-api-key');
const toggleApiKeyVisibilityButton = document.getElementById('toggle-api-key-visibility');
const openrouterLink = document.getElementById('openrouter-link');
const apiKeyStatus = document.getElementById('api-key-status');
const saveSettingsButton = document.getElementById('save-settings');

// Deep Research settings elements
const maxSubQueriesInput = document.getElementById('max-sub-queries');
const urlTimeoutInput = document.getElementById('url-timeout');

// Pinecone settings elements
const pineconeApiKeyInput = document.getElementById('pinecone-api-key');
const togglePineconeKeyVisibilityButton = document.getElementById('toggle-pinecone-key-visibility');
const pineconeEnvironmentInput = document.getElementById('pinecone-environment');
const pineconeIndexNameInput = document.getElementById('pinecone-index-name');
const usePineconeCheckbox = document.getElementById('use-pinecone');
const testPineconeConnectionButton = document.getElementById('test-pinecone-connection');
const uploadToPineconeButton = document.getElementById('upload-to-pinecone');
const pineconeStatus = document.getElementById('pinecone-status');
const cloudStatus = document.getElementById('cloud-status');

// Save settings to localStorage
function saveSettings() {
  try {
    // Save to localStorage
    localStorage.setItem('fungiSettings', JSON.stringify(userSettings));
    console.log('Settings saved to localStorage');
  } catch (err) {
    console.error('Error saving settings to localStorage:', err);
  }
}

// Load settings from localStorage if available
function loadSettings() {
  const savedSettings = localStorage.getItem('fungiSettings');
  if (savedSettings) {
    try {
      const parsedSettings = JSON.parse(savedSettings);
      Object.assign(userSettings, parsedSettings);

      console.log('Loaded settings from localStorage:', userSettings);

      // Apply loaded settings to UI
      defaultRagOnlyRadio.checked = userSettings.defaultAnalysisMethod === 'rag-only';
      defaultRagRadio.checked = userSettings.defaultAnalysisMethod === 'rag';
      defaultModelSelect.value = userSettings.defaultModel;
      autoSwitchRagCheckbox.checked = userSettings.autoSwitchToRag;

      // Apply Intelligent Mode setting
      if (intelligentModeCheckbox) {
        // Explicitly check if the setting exists and is true
        intelligentModeCheckbox.checked = userSettings.intelligentModeEnabled === true;
        console.log('Setting Intelligent Mode checkbox to:', intelligentModeCheckbox.checked);
        updateIntelligentModeBadge();
      }

      // Apply Auto-save Operation Summaries setting
      if (autoSaveSummariesCheckbox) {
        autoSaveSummariesCheckbox.checked = userSettings.autoSaveOperationSummaries === true;
        console.log('Setting Auto-save Operation Summaries checkbox to:', autoSaveSummariesCheckbox.checked);
      }

      // Apply Deep Research mode settings
      deepResearchToggle.checked = userSettings.deepResearchEnabled;
      searchResultsCount.value = userSettings.deepResearchResultsCount;

      // Apply new Deep Research settings
      if (maxSubQueriesInput) {
        maxSubQueriesInput.value = userSettings.deepResearchMaxSubQueries || 5;
      }
      if (urlTimeoutInput) {
        urlTimeoutInput.value = userSettings.deepResearchUrlTimeout || 10;
      }

      // Show/hide Deep Research options based on toggle state
      if (userSettings.deepResearchEnabled) {
        deepResearchOptions.classList.remove('hidden');
        updateModeBadge('deep-research');
        queryInput.placeholder = 'Enter a research question to search the web...';
      } else {
        deepResearchOptions.classList.add('hidden');
      }

      // Set API key if available
      if (userSettings.openrouterApiKey) {
        openrouterApiKeyInput.value = userSettings.openrouterApiKey;

        // Set the API key in the backend
        ipcRenderer.invoke('set-api-key', userSettings.openrouterApiKey)
          .then(result => {
            if (result && result.success) {
              apiKeyStatus.textContent = 'API key loaded';
              apiKeyStatus.className = 'success';
            }
          })
          .catch(error => {
            console.error('Error setting API key:', error);
          });
      }

      // Apply Pinecone settings if available
      if (userSettings.pineconeApiKey) {
        pineconeApiKeyInput.value = userSettings.pineconeApiKey;
        pineconeEnvironmentInput.value = userSettings.pineconeEnvironment || '';
        pineconeIndexNameInput.value = userSettings.pineconeIndexName || '';
        usePineconeCheckbox.checked = userSettings.usePinecone || false;

        // Update cloud status indicator
        updateCloudStatus(userSettings.usePinecone);
      }

      // Also update the model select in the analysis tab
      modelSelect.value = userSettings.defaultModel;

      // Apply the user's preferred mode to the mode badge
      // Just update the badge for now, we'll handle the backend communication later
      // when we know the Python process is running
      updateModeBadge(
        userSettings.defaultAnalysisMethod === 'rag-only' ? 'retrieval-only' :
        (userSettings.defaultAnalysisMethod === 'rag' && userSettings.openrouterApiKey) ? 'llm' : 'retrieval-only'
      );
    } catch (e) {
      console.error('Error loading settings:', e);
    }
  }
}

// Update cloud status indicator
function updateCloudStatus(usePinecone) {
  if (cloudStatus) {
    if (usePinecone) {
      cloudStatus.classList.remove('disabled');
      cloudStatus.classList.add('enabled');
      cloudStatus.querySelector('.cloud-text').textContent = 'Cloud Storage';
    } else {
      cloudStatus.classList.remove('enabled');
      cloudStatus.classList.add('disabled');
      cloudStatus.querySelector('.cloud-text').textContent = 'Local Storage';
    }
  }
}

// Update the Intelligent Mode badge visibility based on the setting
function updateIntelligentModeBadge() {
  if (intelligentBadge) {
    console.log('Updating Intelligent Mode badge, enabled =', userSettings.intelligentModeEnabled);

    if (userSettings.intelligentModeEnabled === true) {
      console.log('Showing Intelligent Mode badge');
      intelligentBadge.classList.remove('hidden');

      // Clear any previous error status when Intelligent Mode is enabled
      clearIntelligentModeError();
    } else {
      console.log('Hiding Intelligent Mode badge');
      intelligentBadge.classList.add('hidden');
    }
  } else {
    console.warn('Intelligent Mode badge element not found');
  }
}

// Clear the Intelligent Mode error status
function clearIntelligentModeError() {
  console.log('Clearing Intelligent Mode error status');
  if (ragStatus && ragStatus.textContent === 'Error in Intelligent Mode') {
    ragStatus.textContent = 'Intelligent Mode ready';
    updateStatus('Ready');

    // Also send a message to the main process to clear the error status
    ipcRenderer.invoke('clear-intelligent-mode-error')
      .catch(error => console.error('Error clearing Intelligent Mode error status:', error));
  }
}

// Apply the user's preferred mode based on settings
async function applyUserPreferredMode() {
  let newMode = '';
  let useLLM = false;

  // Determine the mode based on the user's preferred analysis method
  if (userSettings.defaultAnalysisMethod === 'rag-only') {
    newMode = 'retrieval-only';
    useLLM = false;
  } else if (userSettings.defaultAnalysisMethod === 'rag') {
    // Only set to LLM mode if we have an API key
    if (userSettings.openrouterApiKey) {
      newMode = 'llm';
      useLLM = true;
    } else {
      // Fall back to retrieval-only if no API key is available
      newMode = 'retrieval-only';
      useLLM = false;
    }
  }

  // Update the mode badge
  updateModeBadge(newMode);

  // If the RAG system is already initialized, send the mode change to the backend
  if (ragIndexBuilt && pythonProcessRunning) {
    try {
      // Send the mode change to the Python backend
      console.log(`Sending mode change to backend: useLLM=${useLLM}`);
      await ipcRenderer.invoke('set-rag-mode', { useLLM: useLLM });

      // Update the status
      if (newMode === 'llm') {
        ragStatus.textContent = 'LLM mode activated';
      } else if (newMode === 'retrieval-only') {
        ragStatus.textContent = 'Retrieval-only mode activated';
      }

      // Log the mode change
      addLog(`Switched to ${newMode === 'llm' ? 'LLM' : 'retrieval-only'} mode`);
    } catch (error) {
      console.error('Error changing RAG mode:', error);
    }
  }
}

// Save settings
saveSettingsButton.addEventListener('click', () => {
  // Update settings object
  if (defaultRagOnlyRadio.checked) {
    userSettings.defaultAnalysisMethod = 'rag-only';
  } else if (defaultRagRadio.checked) {
    userSettings.defaultAnalysisMethod = 'rag';
  } else {
    // Default to rag-only if somehow neither is checked
    userSettings.defaultAnalysisMethod = 'rag-only';
  }
  userSettings.defaultModel = defaultModelSelect.value;
  userSettings.autoSwitchToRag = autoSwitchRagCheckbox.checked;
  userSettings.openrouterApiKey = openrouterApiKeyInput.value;

  // Update Intelligent Mode setting
  userSettings.intelligentModeEnabled = intelligentModeCheckbox.checked;
  updateIntelligentModeBadge();

  // Update Auto-save Operation Summaries setting
  userSettings.autoSaveOperationSummaries = autoSaveSummariesCheckbox.checked;

  // Update Deep Research settings
  if (maxSubQueriesInput) {
    userSettings.deepResearchMaxSubQueries = parseInt(maxSubQueriesInput.value) || 5;
  }
  if (urlTimeoutInput) {
    userSettings.deepResearchUrlTimeout = parseInt(urlTimeoutInput.value) || 10;
  }

  // Update Pinecone settings
  userSettings.pineconeApiKey = pineconeApiKeyInput.value;
  userSettings.pineconeEnvironment = pineconeEnvironmentInput.value;
  userSettings.pineconeIndexName = pineconeIndexNameInput.value;
  userSettings.usePinecone = usePineconeCheckbox.checked;

  // Save to localStorage
  localStorage.setItem('fungiSettings', JSON.stringify(userSettings));

  // Set the API key in the backend
  if (openrouterApiKeyInput.value) {
    ipcRenderer.invoke('set-api-key', openrouterApiKeyInput.value)
      .then(result => {
        if (result.success) {
          apiKeyStatus.textContent = 'API key saved successfully';
          apiKeyStatus.className = 'success';

          // Update the mode badge if we have a valid API key and the user has selected RAG with LLM
          if (modeBadge && userSettings.defaultAnalysisMethod === 'rag') {
            updateModeBadge('llm');
          }
        } else {
          apiKeyStatus.textContent = 'Error saving API key: ' + (result.error || 'Unknown error');
          apiKeyStatus.className = 'error';
        }
      })
      .catch(error => {
        apiKeyStatus.textContent = 'Error: ' + error.message;
        apiKeyStatus.className = 'error';
      });
  }

  // Configure Pinecone if API key and index name are provided (environment is optional in v2 API)
  if (pineconeApiKeyInput.value && pineconeIndexNameInput.value) {
    ipcRenderer.invoke('configure-pinecone', {
      apiKey: pineconeApiKeyInput.value,
      environment: pineconeEnvironmentInput.value, // Optional in v2 API
      indexName: pineconeIndexNameInput.value,
      usePinecone: usePineconeCheckbox.checked
    })
    .then(result => {
      if (result.success) {
        pineconeStatus.textContent = 'Pinecone settings saved successfully';
        pineconeStatus.className = 'success';

        // Update cloud status indicator
        updateCloudStatus(usePineconeCheckbox.checked);
      } else {
        pineconeStatus.textContent = 'Error saving Pinecone settings: ' + (result.error || 'Unknown error');
        pineconeStatus.className = 'error';
      }
    })
    .catch(error => {
      pineconeStatus.textContent = 'Error: ' + error.message;
      pineconeStatus.className = 'error';
    });
  }

  // Update the model select in the analysis tab
  modelSelect.value = userSettings.defaultModel;

  // Apply the user's preferred mode immediately
  // This is now an async function, so we need to handle it properly
  applyUserPreferredMode()
    .then(() => {
      // Show confirmation
      addLog('Settings saved successfully');
      updateStatus('Settings saved');

      // Switch back to chat tab (rag tab) to show the applied mode
      switchToTab('rag');
    })
    .catch(error => {
      console.error('Error applying user preferred mode:', error);
      addLog(`Error applying settings: ${error.message}`);

      // Still switch to chat tab
      switchToTab('rag');
    });
});

// API key visibility toggle
toggleApiKeyVisibilityButton.addEventListener('click', () => {
  if (openrouterApiKeyInput.type === 'password') {
    openrouterApiKeyInput.type = 'text';
    toggleApiKeyVisibilityButton.textContent = 'Hide';
  } else {
    openrouterApiKeyInput.type = 'password';
    toggleApiKeyVisibilityButton.textContent = 'Show';
  }
});

// Open OpenRouter website
openrouterLink.addEventListener('click', (e) => {
  e.preventDefault();
  ipcRenderer.invoke('open-external-url', 'https://openrouter.ai/keys');
});

// Pinecone API key visibility toggle
togglePineconeKeyVisibilityButton.addEventListener('click', () => {
  if (pineconeApiKeyInput.type === 'password') {
    pineconeApiKeyInput.type = 'text';
    togglePineconeKeyVisibilityButton.textContent = 'Hide';
  } else {
    pineconeApiKeyInput.type = 'password';
    togglePineconeKeyVisibilityButton.textContent = 'Show';
  }
});

// Test Pinecone connection
testPineconeConnectionButton.addEventListener('click', async () => {
  // Check if we have required fields (API key and index name - environment is optional in v2 API)
  if (!pineconeApiKeyInput.value || !pineconeIndexNameInput.value) {
    pineconeStatus.textContent = 'Please provide Pinecone API key and index name';
    pineconeStatus.className = 'error';
    return;
  }

  // Show testing status
  pineconeStatus.textContent = 'Testing connection...';
  pineconeStatus.className = 'info';

  try {
    // Configure Pinecone first
    await ipcRenderer.invoke('configure-pinecone', {
      apiKey: pineconeApiKeyInput.value,
      environment: pineconeEnvironmentInput.value,
      indexName: pineconeIndexNameInput.value,
      usePinecone: usePineconeCheckbox.checked
    });

    // Test connection
    const result = await ipcRenderer.invoke('test-pinecone-connection');

    if (result.success) {
      pineconeStatus.textContent = 'Testing connection... Please wait for results.';

      // The actual result will come through the Python output handler
      // We'll handle it in the ipcRenderer.on('python-output') handler
    } else {
      pineconeStatus.textContent = 'Error testing connection: ' + (result.error || 'Unknown error');
      pineconeStatus.className = 'error';
    }
  } catch (error) {
    pineconeStatus.textContent = 'Error: ' + error.message;
    pineconeStatus.className = 'error';
  }
});

// Upload vectors to Pinecone
uploadToPineconeButton.addEventListener('click', async () => {
  // Check if we have required fields (API key and index name - environment is optional in v2 API)
  if (!pineconeApiKeyInput.value || !pineconeIndexNameInput.value) {
    pineconeStatus.textContent = 'Please provide Pinecone API key and index name';
    pineconeStatus.className = 'error';
    return;
  }

  // Show uploading status
  pineconeStatus.textContent = 'Starting upload...';
  pineconeStatus.className = 'info';

  // Create progress bar
  const progressContainer = document.createElement('div');
  progressContainer.className = 'upload-progress-container';
  const progressBar = document.createElement('div');
  progressBar.className = 'upload-progress-bar';
  progressContainer.appendChild(progressBar);
  pineconeStatus.appendChild(progressContainer);

  try {
    // Configure Pinecone first
    await ipcRenderer.invoke('configure-pinecone', {
      apiKey: pineconeApiKeyInput.value,
      environment: pineconeEnvironmentInput.value,
      indexName: pineconeIndexNameInput.value,
      usePinecone: usePineconeCheckbox.checked
    });

    // Upload vectors
    const result = await ipcRenderer.invoke('upload-to-pinecone');

    if (result.success) {
      pineconeStatus.textContent = 'Starting upload... Please wait for progress updates.';

      // The actual progress will come through the Python output handler
      // We'll handle it in the ipcRenderer.on('python-output') handler
    } else {
      pineconeStatus.textContent = 'Error uploading vectors: ' + (result.error || 'Unknown error');
      pineconeStatus.className = 'error';
    }
  } catch (error) {
    pineconeStatus.textContent = 'Error: ' + error.message;
    pineconeStatus.className = 'error';
  }
});

// Intelligent Mode checkbox
intelligentModeCheckbox.addEventListener('change', () => {
  const isEnabled = intelligentModeCheckbox.checked;

  // Update the setting
  userSettings.intelligentModeEnabled = isEnabled;

  // Update the badge
  updateIntelligentModeBadge();

  // If enabled, explicitly clear any error status
  if (isEnabled) {
    clearIntelligentModeError();
  }

  // Save the setting to localStorage
  localStorage.setItem('fungiSettings', JSON.stringify(userSettings));

  // Show a notification
  addLog(`Intelligent Mode ${isEnabled ? 'enabled' : 'disabled'}`);

  // Log the current state for debugging
  console.log('Intelligent Mode setting updated:', userSettings.intelligentModeEnabled);
  console.log('Settings saved to localStorage:', JSON.stringify(userSettings));
});

// Auto-save Operation Summaries checkbox
autoSaveSummariesCheckbox.addEventListener('change', () => {
  const isEnabled = autoSaveSummariesCheckbox.checked;

  // Update the setting
  userSettings.autoSaveOperationSummaries = isEnabled;

  // Save the setting to localStorage
  localStorage.setItem('fungiSettings', JSON.stringify(userSettings));

  // Show a notification
  addLog(`Auto-save operation summaries ${isEnabled ? 'enabled' : 'disabled'}`);

  // Log the current state for debugging
  console.log('Auto-save operation summaries setting updated:', userSettings.autoSaveOperationSummaries);
  console.log('Settings saved to localStorage:', JSON.stringify(userSettings));
});

// Use Pinecone checkbox
usePineconeCheckbox.addEventListener('change', async () => {
  const usePinecone = usePineconeCheckbox.checked;

  // Update cloud status indicator
  updateCloudStatus(usePinecone);

  // Save the setting
  userSettings.usePinecone = usePinecone;
  localStorage.setItem('fungiSettings', JSON.stringify(userSettings));

  // Switch vector store if we have required fields (API key and index name - environment is optional in v2 API)
  if (pineconeApiKeyInput.value && pineconeIndexNameInput.value) {
    try {
      const result = await ipcRenderer.invoke('switch-vector-store', usePinecone);

      if (result.success) {
        pineconeStatus.textContent = `Switched to ${usePinecone ? 'Pinecone' : 'local'} vector store`;
        pineconeStatus.className = 'success';
      } else {
        pineconeStatus.textContent = 'Error switching vector store: ' + (result.error || 'Unknown error');
        pineconeStatus.className = 'error';
      }
    } catch (error) {
      pineconeStatus.textContent = 'Error: ' + error.message;
      pineconeStatus.className = 'error';
    }
  }
});

// Note: Settings are now loaded in the DOMContentLoaded event handler
// to ensure they're applied before the UI is fully initialized

// Listen for Intelligent Mode error status updates
ipcRenderer.on('intelligent-mode-error', (_, hasError) => {
  console.log(`Intelligent Mode error status update: ${hasError ? 'Error' : 'No error'}`);

  if (hasError) {
    // Set the error status
    if (ragStatus) {
      ragStatus.textContent = 'Error in Intelligent Mode';
      updateStatus('Error');
    }
  } else {
    // Clear the error status
    clearIntelligentModeError();
  }
});

// Listen for tool switching
ipcRenderer.on('tool-switching', (_, data) => {
  console.log(`Tool switching from ${data.from} to ${data.to} due to: ${data.reason}`);
  addLog(`Switching from ${data.from} to ${data.to}: ${data.reason}`);

  // Update the UI to reflect the tool switch
  if (data.to === 'deep_research') {
    // Switch to Deep Research mode
    setMode('deep-research');

    // IMPORTANT: Ensure we have a valid currentAssistantMessage with processingIndicator
    // This fixes the "Cannot read properties of null (reading 'processingIndicator')" error
    if (!currentAssistantMessage) {
      console.log('Creating new assistant message for Deep Research during tool switching');
      currentAssistantMessage = addChatMessage('assistant', '');

      // Ensure the message is visible
      currentAssistantMessage.style.display = 'block';
      currentAssistantMessage.style.opacity = '1';
      currentAssistantMessage.style.visibility = 'visible';

      // Add a processing indicator if it doesn't exist
      if (!currentAssistantMessage.processingIndicator) {
        console.log('Adding processing indicator for Deep Research');
        const processingDiv = document.createElement('div');
        processingDiv.className = 'processing';
        processingDiv.style.display = 'block';
        processingDiv.style.opacity = '1';
        processingDiv.style.visibility = 'visible';

        processingDiv.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          Searching the web and analyzing results
          <div class="typing-indicator"><span></span><span></span><span></span></div>
        `;

        answerContainer.appendChild(processingDiv);

        // Store the processing indicator to remove it later
        currentAssistantMessage.processingIndicator = processingDiv;

        // Ensure proper scrolling
        scrollChatToBottom();
      }
    }
  } else if (data.to === 'rag_query') {
    // Switch to RAG mode
    setMode('llm');
  }
});

// Function to clean up any stray processing indicators
function cleanupAllProcessingIndicators() {
  console.log('Cleaning up all processing indicators');
  try {
    // Find all processing indicators in the DOM
    const processingIndicators = document.querySelectorAll('.processing');
    if (processingIndicators.length > 0) {
      console.log(`Found ${processingIndicators.length} processing indicators to clean up`);
      processingIndicators.forEach(indicator => {
        try {
          indicator.remove();
          console.log('Removed processing indicator from DOM');
        } catch (err) {
          console.error('Error removing processing indicator from DOM:', err);
        }
      });
    } else {
      console.log('No processing indicators found to clean up');
    }

    // Also check for any error messages about processing indicators
    const errorElements = document.querySelectorAll('p');
    errorElements.forEach(element => {
      if (element.textContent && element.textContent.includes('Cannot read properties of null (reading \'processingIndicator\')')) {
        try {
          element.remove();
          console.log('Removed error message about processingIndicator');
        } catch (err) {
          console.error('Error removing error message:', err);
        }
      }
    });

    // Reset currentAssistantMessage if it's causing issues
    if (currentAssistantMessage && !currentAssistantMessage.isConnected) {
      console.log('Resetting currentAssistantMessage as it is no longer connected to DOM');
      currentAssistantMessage = null;
    }
  } catch (error) {
    console.error('Error in cleanupAllProcessingIndicators:', error);
  }
}

// Add event listener for when the page loads
document.addEventListener('DOMContentLoaded', () => {
  // Clean up any stray processing indicators
  setTimeout(cleanupAllProcessingIndicators, 1000);

  // Clear any Intelligent Mode error status on startup
  setTimeout(() => {
    if (userSettings.intelligentModeEnabled) {
      clearIntelligentModeError();
    }
  }, 2000);
});

// Add event listeners for when responses are complete to clean up any stray indicators
ipcRenderer.on('rag-query-complete', () => {
  // Wait a short time to ensure all processing is done
  setTimeout(cleanupAllProcessingIndicators, 500);
});

ipcRenderer.on('deep-research-complete', () => {
  // Wait a short time to ensure all processing is done
  setTimeout(cleanupAllProcessingIndicators, 500);
});

ipcRenderer.on('intelligent-query-complete', () => {
  // Wait a short time to ensure all processing is done
  setTimeout(cleanupAllProcessingIndicators, 500);
});
