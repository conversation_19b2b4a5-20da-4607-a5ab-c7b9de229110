/* Enhanced Chat UI Styles - Futuristic, Neat, Minimal, Modern */

:root {
  /* Updated color palette for a more modern look */
  --frost-white: rgba(255, 255, 255, 0.8);
  --frost-bg: rgba(255, 255, 255, 0.6);
  --frost-accent: rgba(0, 102, 204, 0.8);
  --frost-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --frost-glow: 0 0 15px rgba(0, 102, 204, 0.3);
  --frost-border: 1px solid rgba(255, 255, 255, 0.18);
  --frost-radius: 16px;
  --frost-blur: blur(10px) saturate(180%);

  /* Animation timing */
  --smooth-transition: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Apply frosted glass effect to chat container */
.chat-container {
  background: linear-gradient(135deg, var(--frost-white), rgba(255, 255, 255, 0.7));
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  border: var(--frost-border);
  box-shadow: var(--frost-shadow);
  border-radius: var(--frost-radius);
  transition: all 0.3s var(--smooth-transition);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px); /* Maximize height while leaving space for header */
  width: 100%; /* Fill the available width */
  max-width: 1600px; /* Set a reasonable max-width for very large screens */
  margin: 0 auto; /* Center the container */
  position: relative; /* Ensure proper positioning context */
  overflow: hidden; /* Contain the content within the container */
}

/* Enhanced chat header */
.chat-header {
  background: linear-gradient(to right, var(--frost-white), rgba(255, 255, 255, 0.9));
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 18px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.03);
  transition: all 0.3s var(--smooth-transition);
}

.chat-header:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    rgba(255, 255, 255, 0),
    rgba(0, 102, 204, 0.2),
    rgba(255, 255, 255, 0));
}

.chat-header h2 {
  font-weight: 500;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, var(--accent-color), #5ac8fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  margin: 0;
  font-size: 1.5rem;
  position: relative;
  transition: all 0.3s var(--smooth-transition);
}

.chat-header h2:hover {
  transform: translateY(-1px);
  text-shadow: 0 2px 5px rgba(0, 102, 204, 0.2);
}

/* Chat status and mode badges */
.chat-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  padding: 6px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s var(--smooth-transition);
}

.chat-status:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

#rag-status {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.3s var(--smooth-transition);
  position: relative;
  padding-left: 18px;
}

#rag-status:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success-color);
  box-shadow: 0 0 8px rgba(52, 199, 89, 0.5);
  animation: pulse 2s infinite;
}

.mode-indicator {
  position: relative;
}

.mode-badge {
  border-radius: 20px;
  padding: 6px 14px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s var(--smooth-transition);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 6px;
}

.mode-badge:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  animation: shimmer 3s infinite;
}

.mode-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mode-badge.llm {
  background: linear-gradient(135deg, var(--accent-color), #5ac8fa);
  box-shadow: 0 2px 10px rgba(0, 102, 204, 0.3);
}

.mode-badge.llm:after {
  content: '🤖';
  font-size: 0.9rem;
  margin-left: 2px;
}

.mode-badge.retrieval-only {
  background: linear-gradient(135deg, var(--success-color), #4cd964);
  box-shadow: 0 2px 10px rgba(52, 199, 89, 0.3);
}

.mode-badge.retrieval-only:after {
  content: '🔍';
  font-size: 0.9rem;
  margin-left: 2px;
}

.mode-badge.deep-research {
  background: linear-gradient(135deg, #9c27b0, #ba68c8);
  box-shadow: 0 2px 10px rgba(156, 39, 176, 0.3);
  animation: pulse-glow 2s infinite;
}

.mode-badge.deep-research:after {
  content: '🔬';
  font-size: 0.9rem;
  margin-left: 2px;
}

.mode-badge.intelligent {
  background: linear-gradient(135deg, #ff9500, #ffcc00);
  box-shadow: 0 2px 10px rgba(255, 149, 0, 0.3);
  font-size: 1.2rem;
  padding: 4px 8px;
  border-radius: 50%;
  margin-left: 8px;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px rgba(255, 149, 0, 0.3); }
  50% { box-shadow: 0 0 15px rgba(255, 149, 0, 0.6); }
  100% { box-shadow: 0 0 5px rgba(255, 149, 0, 0.3); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes pulse {
  0% { opacity: 0.6; transform: translateY(-50%) scale(0.8); }
  50% { opacity: 1; transform: translateY(-50%) scale(1.1); }
  100% { opacity: 0.6; transform: translateY(-50%) scale(0.8); }
}

/* Enhanced chat messages area */
.chat-messages {
  background: linear-gradient(135deg, var(--bg-primary), rgba(215, 220, 230, 0.8));
  padding: 24px;
  gap: 24px;
  flex: 1; /* Take up all available space */
  overflow-y: auto; /* Enable scrolling */
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth; /* Smooth scrolling */
  position: relative; /* Ensure proper positioning context */
  min-height: 200px; /* Ensure minimum height */
  width: 100%; /* Ensure full width */
  max-width: 100%; /* Prevent overflow */

  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 102, 204, 0.3) rgba(0, 0, 0, 0.05);

  /* Add padding at the bottom to ensure messages don't get hidden behind the input container */
  padding-bottom: 120px; /* Reduced padding to allow proper scrolling to bottom */
}

/* Ensure the chat messages container is properly sized */
#answer-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 120px; /* Reduced padding to allow proper scrolling to bottom */
  width: 100%; /* Ensure full width */
  max-width: 100%; /* Prevent overflow */
}

/* Custom scrollbar for Webkit browsers */
.chat-messages::-webkit-scrollbar,
#answer-container::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track,
#answer-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb,
#answer-container::-webkit-scrollbar-thumb {
  background: rgba(0, 102, 204, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
#answer-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 102, 204, 0.5);
}

/* Message container styling */
.message {
  display: flex;
  margin-bottom: 24px;
  opacity: 0;
  max-width: 85%; /* Limit width for better readability */
  position: relative;
}

.message.user {
  align-self: flex-end;
  animation-delay: 0.1s;
}

.message.assistant {
  align-self: flex-start;
  animation-delay: 0.2s;
}

.message.system {
  align-self: center;
  max-width: 90%;
  animation-delay: 0s;
}

/* Message bubbles with enhanced styling */
.message-content {
  border-radius: 20px;
  padding: 18px 22px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s var(--smooth-transition);
  line-height: 1.6;
  position: relative;
  overflow-y: visible !important; /* Ensure content is not cut off */
  max-height: none !important; /* Ensure no height limit is applied */
  display: block !important; /* Ensure message content is always displayed */
  opacity: 1 !important; /* Ensure message content is visible */
  visibility: visible !important; /* Ensure message content is visible */
}

/* User message styling */
.message.user .message-content {
  background: linear-gradient(135deg, var(--accent-color), #5ac8fa);
  color: white;
  border-bottom-right-radius: 4px;
  box-shadow: 0 4px 15px rgba(0, 102, 204, 0.2);
}

.message.user .message-content:hover {
  box-shadow: 0 6px 20px rgba(0, 102, 204, 0.3);
  transform: translateY(-2px) scale(1.01);
}

/* Assistant message styling */
.message.assistant .message-content {
  background: var(--frost-white);
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  border: var(--frost-border);
  border-bottom-left-radius: 4px;
  border-left: 3px solid var(--success-color);
  box-shadow: var(--frost-shadow);
}

.message.assistant .message-content:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px) scale(1.01);
}

/* System message styling */
.message.system .message-content {
  background: rgba(0, 0, 0, 0.03);
  border-left: 3px solid var(--accent-color);
  font-style: italic;
}

/* Enhanced typography for messages */
.message-content p {
  margin-bottom: 12px;
  line-height: 1.6;
}

.message-content strong {
  font-weight: 600;
  color: var(--accent-color);
}

.message.user .message-content strong {
  color: rgba(255, 255, 255, 0.9);
}

/* Enhanced code blocks */
.code-block-wrapper {
  position: relative;
  margin: 20px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  background: rgba(0, 0, 0, 0.02);
  transition: all 0.3s var(--smooth-transition);
}

.code-block-wrapper:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.code-language {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--accent-color);
  color: white;
  font-size: 0.7rem;
  padding: 3px 10px;
  border-bottom-left-radius: 8px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  opacity: 0.9;
}

.message-content pre {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 0 0 10px 10px;
  padding: 18px;
  margin: 0;
  overflow-x: auto;
  border-left: 3px solid var(--accent-color);
  /* Removed max-height limit to allow full code display */
}

.message-content code {
  font-family: 'SF Mono', Menlo, Monaco, 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.5;
}

.inline-code {
  background: rgba(0, 0, 0, 0.04);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
  color: var(--accent-color);
  white-space: nowrap;
}

.copy-code-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.code-block-wrapper:hover .copy-code-button {
  opacity: 1;
}

.copy-code-button:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.copy-code-button svg {
  color: var(--accent-color);
}

/* Enhanced links */
.message-content a {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 1px dotted var(--accent-color);
  padding-bottom: 1px;
}

.message-content a:hover {
  color: var(--accent-hover);
  border-bottom: 1px solid var(--accent-hover);
}

.message.user .message-content a {
  color: white;
  border-bottom: 1px dotted rgba(255, 255, 255, 0.7);
}

.message.user .message-content a:hover {
  border-bottom: 1px solid white;
}

/* Enhanced lists */
.message-content ul,
.message-content ol {
  padding-left: 20px;
  margin: 12px 0;
}

.message-content li {
  margin-bottom: 6px;
}

/* Tools Knowledge Base Styling */
.tool-heading, .procedure-heading {
  margin-top: 20px;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(0, 102, 204, 0.2);
  color: var(--accent-color);
  font-weight: 600;
}

.tool-heading {
  color: #0066cc;
}

.procedure-heading {
  color: #9c27b0;
}

.tool-description, .tool-context {
  margin-bottom: 15px;
  line-height: 1.5;
}

.tool-usage, .tool-examples, .tool-steps {
  margin: 15px 0;
  padding: 15px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border-left: 3px solid var(--accent-color);
}

.tool-usage strong, .tool-examples strong, .tool-steps strong {
  display: block;
  margin-bottom: 8px;
  color: var(--accent-color);
}

.tool-examples ul, .tool-steps ol {
  margin-top: 10px;
  margin-bottom: 0;
  padding-left: 25px;
}

.tool-examples li, .tool-steps li {
  margin-bottom: 8px;
}

.tool-tags {
  margin-top: 15px;
  font-size: 0.9em;
}

.tool-tags .tags {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 5px;
}

.relevance-score {
  margin-top: 10px;
  font-size: 0.9em;
  color: #666;
}

.relevance-score .score {
  font-weight: 600;
  color: var(--accent-color);
}

.tool-separator {
  margin: 25px 0;
  border: 0;
  height: 1px;
  background: linear-gradient(to right,
    rgba(0, 0, 0, 0),
    rgba(0, 102, 204, 0.2),
    rgba(0, 0, 0, 0));
}

/* Intelligent Mode Styling */
.intelligent-reasoning,
.intelligent-tool,
.intelligent-plan,
.intelligent-response,
.intelligent-instruction,
.intelligent-error,
.intelligent-success {
  margin: 15px 0;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.intelligent-reasoning {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05), rgba(255, 204, 0, 0.1));
  border-left: 3px solid #ff9500;
}

.intelligent-reasoning h3 {
  color: #ff9500;
  margin-top: 0;
  font-weight: 600;
}

.intelligent-tool {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(64, 156, 255, 0.1));
  border-left: 3px solid #007aff;
}

.intelligent-tool h3 {
  color: #007aff;
  margin-top: 0;
  font-weight: 600;
}

.intelligent-plan {
  background: rgba(0, 0, 0, 0.02);
  border-left: 3px solid #5ac8fa;
  margin-left: 20px;
  margin-top: -5px;
}

.intelligent-plan h4 {
  color: #5ac8fa;
  margin-top: 0;
  font-weight: 600;
}

.intelligent-response {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.05), rgba(76, 217, 100, 0.1));
  border-left: 3px solid #34c759;
}

.intelligent-response h3 {
  color: #34c759;
  margin-top: 0;
  font-weight: 600;
}

.intelligent-instruction {
  background: linear-gradient(135deg, rgba(90, 200, 250, 0.05), rgba(120, 213, 250, 0.1));
  border-left: 3px solid #5ac8fa;
}

.intelligent-instruction h3 {
  color: #5ac8fa;
  margin-top: 0;
  font-weight: 600;
}

.intelligent-error {
  background: linear-gradient(135deg, rgba(255, 59, 48, 0.05), rgba(255, 100, 90, 0.1));
  border-left: 3px solid #ff3b30;
}

.intelligent-error h3 {
  color: #ff3b30;
  margin-top: 0;
  font-weight: 600;
}

.intelligent-success {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.05), rgba(76, 217, 100, 0.1));
  border-left: 3px solid #34c759;
}

.intelligent-success h3 {
  color: #34c759;
  margin-top: 0;
  font-weight: 600;
}

.response-content {
  margin-top: 10px;
  line-height: 1.6;
}

.source-list {
  margin-top: 10px;
  padding-left: 20px;
}

.source-list li {
  margin-bottom: 5px;
}

.source-list a {
  color: #007aff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.source-list a:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Enhanced tables */
.message-content table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 16px 0;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.message-content th {
  background: rgba(0, 102, 204, 0.08);
  color: var(--accent-color);
  font-weight: 600;
  text-align: left;
  padding: 12px 15px;
}

.message-content td {
  padding: 10px 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.message-content tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Enhanced blockquotes */
.message-content blockquote {
  border-left: 3px solid var(--accent-color);
  padding: 10px 15px;
  margin: 12px 0;
  background: rgba(0, 102, 204, 0.05);
  border-radius: 6px;
  font-style: italic;
}

/* Animations */
@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px rgba(156, 39, 176, 0.5); }
  50% { box-shadow: 0 0 20px rgba(156, 39, 176, 0.8); }
  100% { box-shadow: 0 0 5px rgba(156, 39, 176, 0.5); }
}

@keyframes fade-up {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in-right {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slide-in-left {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes subtle-pop {
  0% { transform: scale(0.95); opacity: 0; }
  70% { transform: scale(1.02); }
  100% { transform: scale(1); opacity: 1; }
}

/* Apply animations to different message types */
.message.user {
  animation: slide-in-right 0.4s var(--smooth-transition) forwards;
  display: flex !important; /* Ensure user messages are always displayed */
  opacity: 1 !important; /* Ensure user messages are visible */
  transform: translateX(0) !important; /* Ensure user messages are not transformed off-screen */
}

.message.assistant {
  animation: slide-in-left 0.4s var(--smooth-transition) forwards;
}

.message.system {
  animation: fade-in 0.5s var(--smooth-transition) forwards;
}

/* Enhanced content styling with animation */
.enhanced-content, .deep-research-content {
  position: relative;
  animation: subtle-pop 0.4s var(--smooth-transition) forwards;
  max-height: none; /* Ensure no height limit is applied */
  overflow: visible; /* Ensure content is not cut off */
}

.content-paragraph {
  margin-bottom: 16px;
  line-height: 1.7;
  font-size: 1rem;
}

/* Deep Research specific styling */
.deep-research-wrapper {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 100%;
  max-height: none !important;
  overflow: visible !important;
}

.deep-research-content {
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 15px;
  line-height: 1.6;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  max-height: none !important;
  overflow: visible !important;
}

.deep-research-content h1,
.deep-research-content h2,
.deep-research-content h3 {
  color: var(--accent-color);
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
  display: block !important;
}

.deep-research-content h1 {
  font-size: 1.8rem;
}

.deep-research-content h2 {
  font-size: 1.5rem;
}

.deep-research-content h3 {
  font-size: 1.2rem;
}

.sources-section {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(0, 102, 204, 0.05);
  border-radius: 12px;
  border-left: 3px solid var(--accent-color);
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.numbered-item {
  margin-bottom: 12px;
  padding-left: 10px;
}

.numbered-item .number {
  color: var(--accent-color);
  font-weight: 600;
  margin-right: 5px;
}

.content-section {
  margin-bottom: 20px;
  padding-bottom: 10px;
}

/* Read more button for long content */
.read-more-button {
  display: block;
  margin: 20px auto;
  padding: 12px 25px;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s var(--smooth-transition);
  box-shadow: 0 4px 10px rgba(0, 102, 204, 0.2);
  position: relative;
  z-index: 10;
}

.read-more-button:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 102, 204, 0.3);
}

.read-more-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(0, 102, 204, 0.2);
}

.content-preview {
  position: relative;
  padding-bottom: 30px; /* Add space for the gradient */
}

.content-preview::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.95));
  pointer-events: none;
}

/* Style for the full content */
.content-full {
  padding-bottom: 20px;
}

/* Document analysis badge */
.analysis-badge {
  display: inline-block;
  background: linear-gradient(135deg, #ff9500, #ff5e3a);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(255, 149, 0, 0.3);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Highlight boxes for important information */
.highlight {
  background: rgba(0, 102, 204, 0.05);
  border-left: 3px solid var(--accent-color);
  padding: 15px;
  margin: 15px 0;
  border-radius: 8px;
}

.highlight.key-points {
  background: rgba(52, 199, 89, 0.05);
  border-left: 3px solid var(--success-color);
}

.highlight.important {
  background: rgba(255, 149, 0, 0.05);
  border-left: 3px solid var(--warning-color);
}

.highlight.note {
  background: rgba(0, 122, 255, 0.05);
  border-left: 3px solid #007aff;
}

.highlight.warning {
  background: rgba(255, 59, 48, 0.05);
  border-left: 3px solid var(--error-color);
}

/* Content headings */
.content-heading {
  margin: 20px 0 15px 0;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
}

.content-heading::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--accent-color);
  border-radius: 1px;
}

/* Document source styling */
.document-source {
  background: rgba(255, 149, 0, 0.05);
  border-radius: 10px;
  padding: 12px 15px;
  margin-top: 20px;
}

.document-source h4 {
  color: var(--warning-color);
  font-size: 0.85rem;
  margin: 0 0 8px 0;
}

.document-name {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Source list styling */
.source-list {
  margin: 10px 0 0 0;
  padding-left: 0;
  list-style-type: none;
}

.source-list li {
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: rgba(0, 102, 204, 0.05);
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
}

.source-list li:hover {
  background-color: rgba(0, 102, 204, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.source-link {
  color: var(--accent-color);
  text-decoration: none;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s ease;
  padding: 2px 0;
}

.source-link:hover {
  text-decoration: underline;
}

/* Remove the bullet point since we're using numbered sources now */
.source-list li::before {
  display: none;
}

.source-link {
  font-size: 0.9rem;
  word-break: break-all;
}

/* Sources section styling */
.sources-section {
  margin-top: 25px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.sources-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

/* Enhanced processing indicator */
.processing {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background: var(--frost-white);
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  border: var(--frost-border);
  border-radius: 16px;
  box-shadow: var(--frost-shadow);
  color: var(--text-secondary);
  font-style: italic;
  align-self: center;
  margin: 15px 0;
  animation: subtle-pop 0.5s var(--smooth-transition) forwards;
  position: relative;
  overflow: hidden;
}

.processing:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

.typing-indicator {
  display: flex;
  gap: 6px;
  margin-left: 5px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-color), #5ac8fa);
  box-shadow: 0 0 5px rgba(0, 102, 204, 0.3);
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-6px); }
}

/* Enhanced chat input area */
.chat-input-container {
  background: var(--frost-white);
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  border: var(--frost-border);
  border-radius: var(--frost-radius);
  box-shadow: var(--frost-shadow);
  padding: 15px 20px;
  transition: all 0.3s var(--smooth-transition);
  position: fixed;
  bottom: 20px; /* Proper spacing from bottom */
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000; /* Higher z-index to ensure it stays on top */
  width: calc(100% - 80px); /* Account for padding and margins */
  max-width: 1560px; /* Match the chat container max-width minus padding */
  margin-bottom: 0; /* Ensure no additional margin is pushing it down */
  max-height: 150px; /* Reduced max height to prevent blocking content */
  overflow-y: auto; /* Add scrolling if content exceeds max height */
}

.chat-input-container:focus-within {
  box-shadow: var(--frost-glow), var(--frost-shadow);
  transform: translateX(-50%) translateY(-2px);
}

.chat-input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  position: relative;
}

#queryInput {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 15px;
  border-radius: 10px;
  font-size: 1rem;
  color: var(--text-primary);
  transition: all 0.3s var(--smooth-transition);
}

#queryInput:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.5);
}

/* Button icon wrapper */
.button-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Send button */
.send-button {
  background: linear-gradient(135deg, var(--accent-color), #5ac8fa);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 10px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s var(--smooth-transition);
  box-shadow: 0 4px 15px rgba(0, 102, 204, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.send-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  transition: transform 0.6s var(--smooth-transition);
}

.send-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 102, 204, 0.3);
}

.send-button:hover:before {
  transform: translateX(100%);
}

.send-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(0, 102, 204, 0.2);
}

.send-button:disabled {
  background: linear-gradient(135deg, #ccc, #ddd);
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

.send-button svg {
  transition: transform 0.3s var(--smooth-transition);
}

.send-button:hover svg {
  transform: translateX(3px);
}

.send-text {
  font-size: 0.95rem;
  letter-spacing: 0.5px;
}

/* File attachment button */
.attachment-button {
  background: transparent;
  border: 1px solid rgba(0, 102, 204, 0.3);
  border-radius: 12px;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s var(--smooth-transition);
  color: var(--accent-color);
  position: relative;
  overflow: hidden;
}

.attachment-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(0, 102, 204, 0.1) 0%, rgba(0, 102, 204, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.3s var(--smooth-transition);
}

.attachment-button:hover {
  background: rgba(0, 102, 204, 0.05);
  transform: translateY(-2px);
  border-color: rgba(0, 102, 204, 0.5);
}

.attachment-button:hover:before {
  opacity: 1;
  transform: scale(1.5);
}

.attachment-button:active {
  transform: translateY(1px);
}

.attachment-button svg {
  transition: transform 0.3s var(--smooth-transition);
}

.attachment-button:hover svg {
  transform: rotate(-10deg);
}

/* Document preview area */
.document-preview {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.5));
  backdrop-filter: var(--frost-blur);
  -webkit-backdrop-filter: var(--frost-blur);
  border-radius: 12px;
  padding: 12px 16px;
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid rgba(0, 102, 204, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
  transition: all 0.3s var(--smooth-transition);
  animation: subtle-pop 0.4s var(--smooth-transition) forwards;
}

.document-preview:hover {
  border-color: rgba(0, 102, 204, 0.2);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.document-preview-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-color), #5ac8fa);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 102, 204, 0.2);
  transition: all 0.3s var(--smooth-transition);
}

.document-preview:hover .document-icon {
  transform: rotate(-5deg);
  box-shadow: 0 6px 15px rgba(0, 102, 204, 0.3);
}

.document-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.document-name {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-primary);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-size {
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.document-preview-remove {
  background: transparent;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s var(--smooth-transition);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.document-preview-remove:hover {
  opacity: 1;
  background: rgba(255, 59, 48, 0.1);
  transform: rotate(90deg);
}

/* Input options styling */
.input-options {
  margin-bottom: 10px; /* Reduced from 15px */
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  padding: 8px 15px; /* Reduced vertical padding from 10px to 8px */
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.deep-research-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.toggle-info {
  color: var(--accent-color);
  cursor: help;
  font-size: 0.9rem;
}

.deep-research-options {
  margin-top: 8px; /* Reduced from 10px */
  padding-top: 8px; /* Reduced from 10px */
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.deep-research-options.hidden {
  display: none;
}

.form-group {
  margin-bottom: 6px; /* Reduced from 10px */
}

.form-group label {
  display: block;
  margin-bottom: 3px; /* Reduced from 5px */
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.form-group input[type="number"] {
  width: 60px;
  padding: 5px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.help-text {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  font-style: italic;
}
