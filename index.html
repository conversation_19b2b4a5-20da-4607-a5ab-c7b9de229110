<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fungi - Knowledge Assistant</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="additional-styles.css">
  <link rel="stylesheet" href="chat-styles.css">
  <link rel="stylesheet" href="import_styles.css">
  <link rel="stylesheet" href="enhanced-chat-styles.css">
  <link rel="stylesheet" href="session-styles.css">
  <link rel="stylesheet" href="loading-screen.css">
  <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Text:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-logo">
      <h1 data-text="FUNGI">FUNGI</h1>
      <p>Web Knowledge Assistant</p>
    </div>
    <div class="loading-spinner"></div>
    <div id="loading-status" class="loading-status">Initializing system...</div>
    <div class="loading-progress">
      <div id="loading-progress-bar" class="loading-progress-bar"></div>
    </div>
  </div>

  <!-- Modern minimal UI -->
  <div class="app-container chat-layout">
    <header>
      <div class="logo">
        <h1 data-text="FUNGI">FUNGI</h1>
        <p>Web Knowledge Assistant</p>
      </div>
      <div class="status-indicator">
        <span id="status-text" data-text="Ready">Ready</span>
      </div>
    </header>

    <div class="main-layout">
      <!-- Sidebar for navigation -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h2>Actions</h2>
        </div>
        <nav class="sidebar-nav">
          <button class="nav-button active" data-tab="rag">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
            Chat
          </button>
          <button class="nav-button" data-tab="setup">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
            Data Sources
          </button>
          <button class="nav-button" data-tab="crawl">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>
            Crawl Progress
          </button>
          <button class="nav-button" data-tab="analysis">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path></svg>
            Analysis
          </button>
          <button class="nav-button" data-tab="import">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>
            Import Documents
          </button>
          <button class="nav-button" data-tab="settings">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
            Settings
          </button>
        </nav>
        <!-- Session Management -->
        <div class="sessions-container">
          <div class="sessions-header">
            <h3>Chat Sessions</h3>
            <button id="new-session-button" class="new-session-button" title="Create new session">+</button>
          </div>
          <div id="sessions-list" class="sessions-list">
            <!-- Sessions will be added here dynamically -->
          </div>
        </div>

        <div class="sidebar-footer">
          <div id="database-status" class="database-status">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path></svg>
            <span>Database Ready</span>
          </div>
        </div>
      </div>

      <!-- Main content area -->
      <div class="content-area">
        <div class="tab-content">
        <!-- Setup Tab -->
        <div class="tab-pane" id="setup-tab">
          <div class="card">
            <h2>Data Sources</h2>

            <div class="setup-sections">
              <!-- Web Crawler Section -->
              <div class="setup-section">
                <h3>Web Crawler</h3>
                <div class="form-group">
                  <label for="url-input">Starting URL:</label>
                  <input type="text" id="url-input" placeholder="https://example.com">
                </div>

                <div class="form-group">
                  <label for="pages-input">Maximum Pages to Crawl:</label>
                  <input type="number" id="pages-input" min="1" max="1000" value="20">
                </div>

                <div class="form-group">
                  <label for="allowed-paths-input">Allowed Subdomain Paths (Optional):</label>
                  <textarea id="allowed-paths-input" placeholder="Enter paths to follow, one per line. Example: /EnactedLegislation/Statutes/HTML/ByChapter/"></textarea>
                  <p class="help-text">Only URLs containing these paths will be crawled. Leave empty to crawl all pages in the domain.</p>
                </div>

                <div class="form-group">
                  <label>Browser Backends:</label>
                  <div class="checkbox-group">
                    <label class="checkbox-label">
                      <input type="checkbox" id="playwright-checkbox" checked>
                      Playwright (Recommended)
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="selenium-checkbox" checked>
                      Selenium (Fallback)
                    </label>
                  </div>
                </div>

                <div class="form-group">
                  <label for="driver-input">ChromeDriver Path (optional):</label>
                  <div class="input-with-button">
                    <input type="text" id="driver-input" placeholder="Leave empty to use webdriver-manager">
                    <button id="browse-button" class="button">Browse</button>
                  </div>
                </div>

                <div class="button-group">
                  <button id="start-button" class="button primary">Start Crawling</button>
                </div>
              </div>

              <!-- Document Import Section -->
              <div class="setup-section">
                <h3>Document Import</h3>
                <div class="form-group">
                  <p>Import documents from your computer to analyze with the RAG system.</p>
                  <div class="supported-formats">
                    <span class="format-badge pdf">PDF</span>
                    <span class="format-badge docx">DOCX</span>
                    <span class="format-badge csv">CSV</span>
                    <span class="format-badge xlsx">XLSX</span>
                    <span class="format-badge txt">TXT</span>
                  </div>
                </div>

                <div class="form-group">
                  <label>Import Options:</label>
                  <div class="checkbox-group">
                    <label class="checkbox-label">
                      <input type="checkbox" id="analyze-after-import-checkbox" checked>
                      Analyze documents after import
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="store-original-paths-checkbox" checked>
                      Store original file paths
                    </label>
                  </div>
                </div>

                <div class="button-group">
                  <button id="quick-import-button" class="button primary">Import Documents</button>
                  <button id="advanced-import-button" class="button">Advanced Import</button>
                </div>
              </div>
            </div>

            <!-- Common Actions -->
            <div class="setup-common-actions">
              <button id="chat-button" class="button success">Chat with Existing Data</button>
            </div>
          </div>
        </div>

        <!-- Crawl Progress Tab -->
        <div class="tab-pane" id="crawl-tab">
          <div class="card">
            <h2>Crawling Progress</h2>

            <div class="form-group">
              <label id="crawl-status">Ready</label>
              <div class="progress-bar">
                <div class="progress-bar-fill" id="crawl-progress"></div>
              </div>
            </div>

            <div class="form-group">
              <label>Activity Log:</label>
              <div class="log-container" id="log-container"></div>
            </div>
          </div>
        </div>

        <!-- Analysis Tab -->
        <div class="tab-pane" id="analysis-tab">
          <div class="card">
            <h2>Analysis Options</h2>

            <div class="form-group">
              <label id="analysis-status">Choose an analysis method:</label>
            </div>

            <div class="form-group">
              <label for="model-select">Select AI Model:</label>
              <select id="model-select">
                <option value="mistralai/mistral-7b-instruct">Mistral 7B</option>
                <option value="google/gemini-2.0-flash-001">Gemini 2.0 Flash</option>
                <option value="qwen/qwen3-235b-a22b:free">Qwen 3 235B</option>
                <option value="meta-llama/llama-4-maverick:free">Llama 4 Maverick</option>
                <option value="google/gemini-2.0-flash-exp:free">Gemini 2.0 Flash Exp</option>
              </select>
            </div>

            <div class="button-group analysis-buttons">
              <button id="rag-analysis-button" class="button primary">
                <span class="button-icon">🤖</span>
                <span class="button-text">
                  <span class="button-title">RAG with LLM</span>
                  <span class="button-subtitle">AI-powered answers (requires API key)</span>
                </span>
              </button>
              <button id="rag-only-analysis-button" class="button success">
                <span class="button-icon">🔍</span>
                <span class="button-text">
                  <span class="button-title">RAG Retrieval Only</span>
                  <span class="button-subtitle">Fast answers (no API key needed)</span>
                </span>
              </button>
            </div>

            <div class="form-group">
              <label>Analysis Results:</label>
              <div class="results-container" id="results-container"></div>
            </div>
          </div>
        </div>

        <!-- RAG Q&A Tab (Chat Interface) -->
        <div class="tab-pane active" id="rag-tab">
          <div class="chat-container">
            <div class="chat-header">
              <h2>Knowledge Assistant</h2>
              <div class="chat-status">
                <div class="mode-indicator">
                  <span id="mode-badge" class="mode-badge retrieval-only">Retrieval Only</span>
                  <span id="intelligent-badge" class="mode-badge intelligent hidden" title="Intelligent Mode: LLM can access tools knowledge base">🧠</span>
                </div>
                <span id="rag-status">RAG system not initialized</span>
                <div id="cloud-status" class="cloud-status disabled">
                  <span class="cloud-icon">☁️</span>
                  <span class="cloud-text">Local Storage</span>
                </div>
                <!-- Progress bar will be inserted here dynamically when needed -->
              </div>
            </div>

            <div class="chat-messages" id="answer-container">
              <!-- Welcome message -->
              <div class="message system">
                <div class="message-content">
                  <p>Welcome to Fungi Knowledge Assistant! Ask me anything about the website content.</p>
                </div>
              </div>
            </div>

            <div class="chat-input-container">
              <!-- Deep Research Toggle -->
              <div class="input-options">
                <div class="deep-research-toggle">
                  <label class="toggle-switch">
                    <input type="checkbox" id="deep-research-toggle">
                    <span class="toggle-slider"></span>
                  </label>
                  <span class="toggle-label">Deep Research Mode</span>
                  <span class="toggle-info" title="Search the web, extract data from top results, and analyze with AI">ⓘ</span>
                </div>
                <div id="deep-research-options" class="deep-research-options hidden">
                  <div class="form-group">
                    <label for="search-results-count">Results to analyze:</label>
                    <input type="number" id="search-results-count" min="1" max="10" value="3">
                  </div>
                  <div class="form-group">
                    <span class="help-text">Results are not saved to database to ensure fresh data each time</span>
                  </div>
                </div>
              </div>

              <!-- Enhanced Chat Input -->
              <div class="chat-input-wrapper">
                <button id="attach-file-button" class="attachment-button" title="Attach a document">
                  <div class="button-icon-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
                    </svg>
                  </div>
                </button>
                <input type="text" id="query-input" placeholder="Ask a question about the website content...">
                <button id="query-button" class="send-button">
                  <div class="button-icon-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <line x1="22" y1="2" x2="11" y2="13"></line>
                      <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                    </svg>
                  </div>
                  <span class="send-text">Send</span>
                </button>
              </div>

              <!-- Document Preview -->
              <div id="attached-file-preview" class="document-preview hidden">
                <div class="document-preview-content">
                  <div class="document-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <line x1="10" y1="9" x2="8" y2="9"></line>
                    </svg>
                  </div>
                  <div class="document-details">
                    <div class="document-name" id="attached-filename">filename.pdf</div>
                    <div class="document-size" id="attached-filesize">(123 KB)</div>
                  </div>
                </div>
                <button id="remove-file-button" class="document-preview-remove" title="Remove document">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Import Documents Tab -->
        <div class="tab-pane" id="import-tab">
          <div class="import-container">
            <div class="import-header">
              <h2>Import Documents</h2>
              <div class="import-actions">
                <button id="select-documents-button" class="button primary">Select Files</button>
              </div>
            </div>

            <div class="import-area">
              <div id="drop-zone" class="drop-zone">
                <div class="drop-zone-icon">📄</div>
                <div class="drop-zone-text">Drag & drop files here</div>
                <div class="drop-zone-subtext">Supported formats: PDF, DOCX, CSV, XLSX, TXT</div>
              </div>

              <div id="file-list" class="file-list hidden">
                <!-- Files will be added here dynamically -->
              </div>

              <div class="import-actions">
                <button id="import-documents-button" class="button primary" disabled>Import Selected Files</button>
                <button id="clear-files-button" class="button">Clear</button>
              </div>
            </div>

            <div id="import-status" class="import-status hidden">
              <!-- Status will be shown here -->
            </div>

            <div id="imported-documents" class="imported-documents hidden">
              <h3>Imported Documents</h3>
              <div id="document-grid" class="document-grid">
                <!-- Imported documents will be shown here -->
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-pane" id="settings-tab">
          <div class="card">
            <h2>Settings</h2>

            <div class="form-group">
              <label>Default Analysis Method:</label>
              <div class="radio-group">
                <label class="radio-label">
                  <input type="radio" name="default-analysis" id="default-rag-only" checked>
                  RAG-based Q&A - Retrieval Only (Fast, No API Key)
                </label>
                <label class="radio-label">
                  <input type="radio" name="default-analysis" id="default-rag">
                  RAG-based Q&A with LLM (Comprehensive, Requires API Key)
                </label>
              </div>
            </div>

            <div class="form-group">
              <label>Default AI Model:</label>
              <select id="default-model">
                <option value="mistralai/mistral-7b-instruct" selected>Mistral 7B</option>
                <option value="google/gemini-2.0-flash-001">Gemini 2.0 Flash</option>
                <option value="qwen/qwen3-235b-a22b:free">Qwen 3 235B</option>
                <option value="meta-llama/llama-4-maverick:free">Llama 4 Maverick</option>
                <option value="google/gemini-2.0-flash-exp:free">Gemini 2.0 Flash Exp</option>
              </select>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="auto-switch-rag" checked>
                Automatically switch to RAG tab after crawl completes
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="intelligent-mode-checkbox">
                Intelligent Mode
              </label>
              <p class="help-text">When enabled, the LLM can query the tools knowledge base to learn how to use system features</p>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="auto-save-summaries-checkbox">
                Auto-save operation summaries
              </label>
              <p class="help-text">When enabled, operation summaries will be automatically saved to the knowledge base</p>
            </div>

            <div class="form-group">
              <h3>Deep Research Settings</h3>
              <p class="help-text">Configure multi-query research behavior</p>

              <div class="form-group">
                <label for="max-sub-queries">Maximum Sub-Queries:</label>
                <input type="number" id="max-sub-queries" min="1" max="10" value="5">
                <p class="help-text">Maximum number of focused search queries to generate (1-10)</p>
              </div>

              <div class="form-group">
                <label for="url-timeout">URL Timeout (seconds):</label>
                <input type="number" id="url-timeout" min="5" max="60" value="10">
                <p class="help-text">Timeout per URL when extracting content (5-60 seconds)</p>
              </div>
            </div>

            <div class="form-group api-key-section">
              <label>OpenRouter API Key:</label>
              <div class="api-key-input">
                <input type="password" id="openrouter-api-key" placeholder="Enter your OpenRouter API key">
                <button id="toggle-api-key-visibility" class="button secondary small">Show</button>
              </div>
              <p class="help-text">Required for LLM mode. Get a key at <a href="#" id="openrouter-link">openrouter.ai</a></p>
              <div id="api-key-status"></div>
            </div>

            <div class="form-group">
              <h3>Cloud Vector Storage (Pinecone)</h3>
              <p class="help-text">Store your vector embeddings in the cloud for persistence and sharing</p>

              <div class="form-group">
                <label>Pinecone API Key:</label>
                <div class="api-key-input">
                  <input type="password" id="pinecone-api-key" placeholder="Enter your Pinecone API key">
                  <button id="toggle-pinecone-key-visibility" class="button secondary small">Show</button>
                </div>
              </div>

              <div class="form-group">
                <label>Pinecone Environment: <span class="optional-label">(Optional in v2 API)</span></label>
                <input type="text" id="pinecone-environment" placeholder="Optional in Pinecone v2 API">
                <p class="help-text">Environment is no longer required in Pinecone v2 API</p>
              </div>

              <div class="form-group">
                <label>Pinecone Index Name:</label>
                <input type="text" id="pinecone-index-name" placeholder="e.g., fungi-vectors">
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="use-pinecone">
                  Use Pinecone as primary vector store
                </label>
                <p class="help-text">When enabled, queries will search Pinecone instead of local storage</p>
              </div>

              <div class="pinecone-actions">
                <button id="test-pinecone-connection" class="button secondary">Test Connection</button>
                <button id="upload-to-pinecone" class="button secondary">Upload Vectors to Pinecone</button>
              </div>

              <div id="pinecone-status"></div>
            </div>

            <button id="save-settings" class="button primary">Save Settings</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Session Creation Modal -->
  <div id="session-modal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Create New Chat Session</h3>
        <button id="modal-close" class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <input type="text" id="session-name-input" placeholder="Enter session name">
      </div>
      <div class="modal-footer">
        <button id="modal-cancel" class="modal-button cancel">Cancel</button>
        <button id="modal-create" class="modal-button confirm">Create</button>
      </div>
    </div>
  </div>

  <script src="debug.js"></script>
  <script src="renderer.js"></script>
</body>
</html>
